body {
  background: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
}

.container-fluid {
  max-width: 1600px;
  padding: 0 24px;
}

h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2c3e50;
}

.dropdown-menu.show {
  z-index: 10000;
}

/* Table styling */
.table {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  margin-bottom: 2rem;
}

.table th {
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 12px 8px;
  vertical-align: middle;
  position: sticky;
  top: 0;
  z-index: 1010;
  background-color: #f8f9fa;
}

.table td {
  padding: 12px 8px;
  vertical-align: middle;
}

.table tr:hover {
  background-color: rgba(0,123,255,0.03);
}

/* Background colors for different sections */
.bg-joli { 
  background: #ffe0ef !important; 
}

.bg-csv { 
  background: #e6ffe0 !important; 
}

/* Card styling */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid rgba(0,0,0,0.08);
}

.card-header {
  font-weight: 600;
  padding: 12px 16px;
}

/* Button styling */
.btn {
  border-radius: 5px;
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.btn-group .btn {
  min-width: auto;
}

.btn-outline-primary:hover {
  background-color: #007bff;
  color: white;
}

.btn-outline-success:hover {
  background-color: #28a745;
  color: white;
}

.btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}

/* Form controls */
.form-control {
  border-radius: 5px;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Alert styling */
.alert {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Floating toolbar */
#bulkToolbar {
  transition: all 0.3s ease;
  padding: 12px 0;
}

/* Empty state styling */
#noDataMessage {
  padding: 60px 0;
  color: #6c757d;
}

#noDataMessage .display-1 {
  font-size: 4rem;
  opacity: 0.2;
}

/* Modal styling */
.modal-content {
  border-radius: 8px;
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.modal-header {
  border-bottom: 1px solid #eee;
  padding: 16px 24px;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  border-top: 1px solid #eee;
  padding: 16px 24px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container-fluid {
    padding: 0 12px;
  }
  
  h1 {
    font-size: 1.5rem;
  }
  
  .btn {
    padding: 0.4rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .table th, .table td {
    padding: 8px 6px;
    font-size: 0.9rem;
  }
}

/* Tooltip styling */
.tooltip {
  font-size: 0.8rem;
}

/* Results list styling */
.result-list {
  margin-top: 1.5rem;
  border-radius: 6px;
  overflow: hidden;
}

.list-group-item {
  border-left: none;
  border-right: none;
  padding: 12px 16px;
}

.list-group-item:first-child {
  border-top: none;
}

.list-group-item:last-child {
  border-bottom: none;
}

/* Sticky elements */
.sticky-top {
  background-color: #f5f7fa;
  z-index: 1020;
  padding-top: 10px;
  padding-bottom: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}
