// Default columns to show
let visibleColumns = {
  sku: true,
  stock: true,
  joliStock: true,
  regular: true,
  sale: true,
  title: true,
  price: true,
  thumbnail: true,
  actions: true
};

window.applyColumnSelection = function() {
  console.log("applyColumnSelection called");
  const form = document.getElementById('columnSelectorForm');
  console.log("Form found:", form);

  // Check if all elements exist before accessing them
  const stockEl = form.querySelector('#col-stock');
  const joliStockEl = form.querySelector('#col-joliStock');
  const regularEl = form.querySelector('#col-regular');
  const saleEl = form.querySelector('#col-sale');
  const titleEl = form.querySelector('#col-title');
  const thumbnailEl = form.querySelector('#col-thumbnail');
  const actionsEl = form.querySelector('#col-actions');

  console.log("Elements found:", {
    stock: stockEl?.checked,
    joliStock: joliStockEl?.checked,
    regular: regularEl?.checked,
    sale: saleEl?.checked,
    title: titleEl?.checked,
    thumbnail: thumbnailEl?.checked,
    actions: actionsEl?.checked
  });

  // Update the visibleColumns object
  visibleColumns = {
    sku: true, // always true
    stock: stockEl ? stockEl.checked : true,
    joliStock: joliStockEl ? joliStockEl.checked : true,
    regular: regularEl ? regularEl.checked : true,
    sale: saleEl ? saleEl.checked : true,
    title: titleEl ? titleEl.checked : true,
    thumbnail: thumbnailEl ? thumbnailEl.checked : true,
    actions: actionsEl ? actionsEl.checked : true
  };

  console.log("Updated visible columns:", visibleColumns);

  // Close the modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('columnSelectorModal'));
  if (modal) {
    modal.hide();
  }

  // Force re-render the table
  renderTable();
};
// app.js: UI logic and event handlers

import { api } from './api.js';

let csvData = [];
let updateResults = [];
let selectedRows = [];
let manualMode = true;
let loading = false;

// Show toast notification (floating, non-blocking)
let maxToasts = 3;

function showAlert(message, type = 'info', timeout = 5000) {
  // Create toast container if it doesn't exist
  let toastContainer = document.getElementById('toastContainer');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toastContainer';
    toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    toastContainer.style.zIndex = '1055';
    document.body.appendChild(toastContainer);
  }

  // Limit number of visible toasts
  const existingToasts = toastContainer.querySelectorAll('.toast');
  if (existingToasts.length >= maxToasts) {
    // Remove oldest toast
    const oldestToast = existingToasts[0];
    const bsToast = bootstrap.Toast.getInstance(oldestToast);
    if (bsToast) {
      bsToast.hide();
    } else {
      oldestToast.remove();
    }
  }

  // Create unique ID for this toast
  const toastId = 'toast-' + Date.now() + '-' + Math.random().toString(36).substring(2, 11);

  // Map alert types to Bootstrap toast classes
  const typeMap = {
    'success': 'text-bg-success',
    'danger': 'text-bg-danger',
    'warning': 'text-bg-warning',
    'info': 'text-bg-info',
    'primary': 'text-bg-primary'
  };

  const toastClass = typeMap[type] || 'text-bg-info';

  // Create toast HTML
  const toastHTML = `
    <div id="${toastId}" class="toast ${toastClass} mb-2" role="alert" aria-live="assertive" aria-atomic="true">
      <div class="toast-header">
        <i class="bi bi-${getIconForType(type)} me-2"></i>
        <strong class="me-auto">${getTypeLabel(type)}</strong>
        <small class="text-muted">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</small>
        <button type="button" class="btn-close btn-close-white ms-2" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        ${message}
      </div>
    </div>
  `;

  // Add toast to container
  toastContainer.insertAdjacentHTML('beforeend', toastHTML);

  // Initialize and show the toast
  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, {
    autohide: timeout > 0,
    delay: timeout
  });

  toast.show();

  // Remove toast element after it's hidden
  toastElement.addEventListener('hidden.bs.toast', () => {
    toastElement.remove();
  });

  // Add click to dismiss functionality
  toastElement.addEventListener('click', () => {
    toast.hide();
  });
}

// Helper function to get icon for alert type
function getIconForType(type) {
  const iconMap = {
    'success': 'check-circle-fill',
    'danger': 'exclamation-triangle-fill',
    'warning': 'exclamation-triangle-fill',
    'info': 'info-circle-fill',
    'primary': 'info-circle-fill'
  };
  return iconMap[type] || 'info-circle-fill';
}

// Helper function to get label for alert type
function getTypeLabel(type) {
  const labelMap = {
    'success': 'Success',
    'danger': 'Error',
    'warning': 'Warning',
    'info': 'Info',
    'primary': 'Info'
  };
  return labelMap[type] || 'Info';
}

window.addManualRow = function() {
  csvData.push({ sku: '', stock: 1, prices: { regular: '', sale: '' } });
  selectedRows.push(true);
  renderTable();
  showAlert('Row added. Enter SKU and stock.', 'info', 1500);
};

// Add test data function for debugging
window.addTestData = function() {
  csvData = [
    { sku: 'TEST001', stock: 10, prices: { regular: '29.99', sale: '19.99' }, title: 'Test Product 1', joliStock: 8, joliRegular: '29.99', joliSale: '19.99' },
    { sku: 'TEST002', stock: 5, prices: { regular: '49.99', sale: '39.99' }, title: 'Test Product 2', joliStock: 3, joliRegular: '49.99', joliSale: '39.99' },
    { sku: 'TEST003', stock: 0, prices: { regular: '15.99', sale: '12.99' }, title: 'Test Product 3', joliStock: 0, joliRegular: '15.99', joliSale: '12.99' }
  ];
  selectedRows = Array(csvData.length).fill(true);
  renderTable();
  showAlert('Test data added!', 'success');
};


window.renderTable = function() {
  console.log("Rendering table with visible columns:", visibleColumns);
  const tbody = document.getElementById('dataTableBody');
  tbody.innerHTML = '';
  
  // Update table headers based on visible columns
  const thead = document.querySelector('#dataTable thead tr');
  if (thead) {
    thead.innerHTML = '';
    
    // Checkbox column (always visible)
    thead.innerHTML += `<th style="width:40px;"><input type="checkbox" class="form-check-input" id="select-all" onchange="selectAllRows(this.checked)" ${selectedRows.every(s => s) ? 'checked' : ''} /></th>`;
    
    // Thumbnail column
    if (visibleColumns.thumbnail) {
      thead.innerHTML += `<th>Thumbnail</th>`;
    }
    
    // SKU column (always visible)
    thead.innerHTML += `<th>SKU</th>`;
    
    // Title column
    if (visibleColumns.title) {
      thead.innerHTML += `<th>Title</th>`;
    }
    
    // Joli Stock column
    if (visibleColumns.joliStock) {
      thead.innerHTML += `<th class="bg-joli">Joli Stock</th>`;
    }
    
    // Joli Regular Price column
    if (visibleColumns.regular) {
      thead.innerHTML += `<th class="bg-joli">Joli Regular</th>`;
    }

    // Joli Sale Price column
    if (visibleColumns.sale) {
      thead.innerHTML += `<th class="bg-joli">Joli Sale</th>`;
    }

    // CSV/XLSX Stock column
    if (visibleColumns.stock) {
      thead.innerHTML += `<th class="bg-csv">CSV/XLSX Stock</th>`;
    }

    // CSV/XLSX Regular Price column
    if (visibleColumns.regular) {
      thead.innerHTML += `<th class="bg-csv">CSV/XLSX Regular</th>`;
    }

    // CSV/XLSX Sale Price column
    if (visibleColumns.sale) {
      thead.innerHTML += `<th class="bg-csv">CSV/XLSX Sale</th>`;
    }
    
    // Actions column
    if (visibleColumns.actions) {
      thead.innerHTML += `<th style="width:80px;">Actions</th>`;
    }
  }
  
  if (csvData.length === 0) {
    toggleEmptyState();
    document.getElementById('bulkToolbar').classList.add('d-none');
    return;
  }
  
  toggleEmptyState();
  
  // Check if any rows are selected
  const anySelected = selectedRows.some(selected => selected);
  const selectedCount = selectedRows.filter(selected => selected).length;
  
  // Update selected count
  document.getElementById('selectedCount').textContent = `${selectedCount} selected`;
  
  // Show/hide bulk toolbar
  if (anySelected) {
    document.getElementById('bulkToolbar').classList.remove('d-none');
  } else {
    document.getElementById('bulkToolbar').classList.add('d-none');
  }
  
  // Render table rows
  csvData.forEach((row, idx) => {
    const tr = document.createElement('tr');
    
    // Checkbox column (always visible)
    let td = document.createElement('td');
    td.innerHTML = `<input type="checkbox" class="form-check-input" ${selectedRows[idx] ? 'checked' : ''} onchange="toggleRowSelection(${idx}, this.checked)" />`;
    tr.appendChild(td);
    
    // Thumbnail column
    if (visibleColumns.thumbnail) {
      td = document.createElement('td');
      td.id = `joli-thumb-${idx}`;
      td.className = 'text-center';
      td.innerHTML = row.thumb ? `<a href="${row.productUrl || '#'}" target="_blank" data-bs-toggle="tooltip" data-bs-title="Open product page"><img src="${row.thumb}" style="max-width:60px;max-height:60px;"></a>` : '';
      tr.appendChild(td);
    }
    
    // SKU column (always visible)
    td = document.createElement('td');
    td.innerHTML = `<input type="text" class="form-control form-control-sm" value="${row.sku || ''}" onchange="updateSkuValue(${idx}, this.value)" onblur="getJoliStock(this.value, ${idx})" />`;
    tr.appendChild(td);
    
    // Title column
    if (visibleColumns.title) {
      td = document.createElement('td');
      td.id = `joli-title-${idx}`;
      td.textContent = row.title || '';
      tr.appendChild(td);
    }
    
    // Joli Stock column
    if (visibleColumns.joliStock) {
      td = document.createElement('td');
      td.id = `joli-stock-${idx}`;
      td.className = 'bg-joli';
      td.textContent = row.joliStock !== undefined ? row.joliStock : '-';
      tr.appendChild(td);
    }
    
    // Joli Regular Price column
    if (visibleColumns.regular) {
      td = document.createElement('td');
      td.id = `joli-regular-${idx}`;
      td.className = 'bg-joli';
      td.textContent = row.joliRegular !== undefined ? row.joliRegular : '';
      tr.appendChild(td);
    }
    
    // Joli Sale Price column
    if (visibleColumns.sale) {
      td = document.createElement('td');
      td.id = `joli-sale-${idx}`;
      td.className = 'bg-joli';
      td.textContent = row.joliSale !== undefined ? row.joliSale : '';
      tr.appendChild(td);
    }
    
    // CSV/XLSX Stock column
    if (visibleColumns.stock) {
      td = document.createElement('td');
      td.className = 'bg-csv';
      td.innerHTML = `<input type="number" id="stock-input-${idx}" class="form-control form-control-sm" value="${row.stock || ''}" min="0" onchange="updateStockValue(${idx}, this.value)" />`;
      tr.appendChild(td);
    }
    
    // CSV/XLSX Regular Price column
    if (visibleColumns.regular) {
      td = document.createElement('td');
      td.className = 'bg-csv';
      td.innerHTML = `<input type="text" class="form-control form-control-sm" value="${row.prices?.regular || ''}" onchange="updateRegularPriceValue(${idx}, this.value)" />`;
      tr.appendChild(td);
    }
    
    // CSV/XLSX Sale Price column
    if (visibleColumns.sale) {
      td = document.createElement('td');
      td.className = 'bg-csv';
      td.innerHTML = `<input type="text" class="form-control form-control-sm" value="${row.prices?.sale || ''}" onchange="updateSalePriceValue(${idx}, this.value)" />`;
      tr.appendChild(td);
    }
    
    // Actions column
    if (visibleColumns.actions) {
      td = document.createElement('td');
      td.innerHTML = `
        <div class="btn-group">
          <button class="btn btn-sm btn-outline-primary" onclick="getJoliStock('${row.sku || ''}', ${idx})" title="Get Info">
            <i class="bi bi-cloud-download"></i>
          </button>
          <button class="btn btn-sm btn-outline-success" onclick="updateJoliStockFromRow(${idx})" title="Update Stock">
            <i class="bi bi-box-seam"></i>
          </button>
          <button class="btn btn-sm btn-outline-info" onclick="showProductDetailsModal(${idx})" title="Details">
            <i class="bi bi-info-circle"></i>
          </button>
        </div>
      `;
      tr.appendChild(td);
    }
    
    tbody.appendChild(tr);
  });
  
  // Initialize tooltips
  const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
  tooltips.forEach(el => {
    try {
      new bootstrap.Tooltip(el);
    } catch (e) {
      console.warn("Could not initialize tooltip", e);
    }
  });
};

// Show/hide floating bulk toolbar
window.renderBulkToolbar = function(forceShow) {
  const toolbar = document.getElementById('bulkToolbar');
  const anySelected = selectedRows.some(Boolean);
  if (anySelected || forceShow) {
    toolbar.classList.remove('d-none');
  } else {
    toolbar.classList.add('d-none');
  }
};

window.updateSkuValue = function(idx, value) { 
  csvData[idx].sku = value; 
};

window.updateStockValue = function(idx, value) { 
  csvData[idx].stock = value; 
};

window.getSkuValue = function(idx) { return csvData[idx].sku; };
window.getStockValue = function(idx) { return csvData[idx].stock; };
window.toggleRow = function(idx) { selectedRows[idx] = !selectedRows[idx]; };
window.selectAllRows = function(checked) { selectedRows = Array(csvData.length).fill(checked); renderTable(); };

window.renderResults = function() {
  const resultDiv = document.getElementById('updateResults');
  if (!updateResults.length) { resultDiv.innerHTML = ''; return; }
  let html = '<h4>Update Results</h4><ul class="list-group">';
  updateResults.forEach((r) => {
    html += `<li class="list-group-item d-flex justify-content-between align-items-center">
      <span><b>SKU:</b> ${r.sku}</span>
      <span class="badge bg-${r.status === 'success' ? 'success' : 'danger'}">${r.status}</span>
      ${r.message ? `<span class="text-danger ms-2">${r.message}</span>` : ''}
    </li>`;
  });
  html += '</ul>';
  resultDiv.innerHTML = html;
};

// Debounce function to prevent excessive API calls
function debounce(func, wait) {
  let timeout;
  return function(...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

// Use debounced version for input changes
window.updateSkuValue = function(idx, value) { 
  csvData[idx].sku = value; 
};

window.updateStockValue = function(idx, value) { 
  csvData[idx].stock = value; 
};

// Debounced version of getJoliStock
const debouncedGetJoliStock = debounce((sku, idx) => {
  if (!sku) return;
  api.getJoliStock(sku).then((data) => {
    // Update DOM
    document.getElementById('joli-stock-' + idx).innerText =
      data.stock !== undefined ? data.stock : data.exists ? 'Exists' : 'Not found';
    document.getElementById('joli-title-' + idx).innerText = data.title || '';
    document.getElementById('joli-regular-' + idx).innerText = data.regular_price !== undefined ? data.regular_price : '';
    document.getElementById('joli-sale-' + idx).innerText = data.sale_price !== undefined ? data.sale_price : '';
    
    // Make thumbnail clickable with product URL
    const thumbContainer = document.getElementById('joli-thumb-' + idx);
    if (data.thumbnail) {
      // Use the product_url from the API if available
      const productUrl = data.product_url || '';
      thumbContainer.innerHTML = `<a href="${productUrl}" target="_blank" data-bs-toggle="tooltip" data-bs-title="Open product page"><img src="${data.thumbnail}" style="max-width:60px;max-height:60px;"></a>`;
    } else {
      thumbContainer.innerHTML = '';
    }
    
    // Update csvData for export
    csvData[idx].joliStock = data.stock !== undefined ? data.stock : '';
    csvData[idx].joliRegular = data.regular_price !== undefined ? data.regular_price : '';
    csvData[idx].joliSale = data.sale_price !== undefined ? data.sale_price : '';
    csvData[idx].title = data.title || '';
    csvData[idx].thumb = data.thumbnail || '';
    csvData[idx].productUrl = data.product_url || '';
    
    // Initialize tooltips for new elements
    const tooltips = thumbContainer.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(el => new bootstrap.Tooltip(el));
  });
}, 300);

// Replace the original function with the debounced version
window.getJoliStock = function(sku, idx) {
  debouncedGetJoliStock(sku, idx);
};

// Helper function to update stock from row button
window.updateJoliStockFromRow = function(idx) {
  const row = csvData[idx];
  if (!row || !row.sku) {
    showAlert('Please enter a valid SKU', 'warning');
    return;
  }

  // Get current stock value from the input field using the ID
  const stockInput = document.getElementById(`stock-input-${idx}`);
  const currentStock = stockInput ? stockInput.value : row.stock || 0;

  updateJoliStock(row.sku, currentStock, idx);
};

window.updateJoliStock = function(sku, stock, idx) {
  if (!sku) {
    showAlert('Please enter a valid SKU', 'warning');
    return;
  }

  updateResults = [];
  showAlert('Updating stock...', 'info', 2000);

  // Convert stock to a number if possible
  const stockValue = stock !== '' ? Number(stock) : 0;

  api.updateJoliStock(sku, stockValue).then((data) => {
    updateResults.push({
      sku: sku,
      status: data[0]?.status || 'error',
      message: data[0]?.message || ''
    });
    renderResults();
    getJoliStock(sku, idx);
    showAlert('Stock updated!', 'success');
  }).catch((error) => {
    console.error('Stock update error:', error);
    showAlert('Stock update failed!', 'danger');
  });
};

window.getAllJoliStock = function() {
  // Clear any existing data first
  csvData.forEach((_, idx) => {
    let el;
    el = document.getElementById('joli-stock-' + idx); if (el) el.innerText = '-';
    el = document.getElementById('joli-title-' + idx); if (el) el.innerText = '';
    el = document.getElementById('joli-regular-' + idx); if (el) el.innerText = '';
    el = document.getElementById('joli-sale-' + idx); if (el) el.innerText = '';
    el = document.getElementById('joli-thumb-' + idx); if (el) el.innerHTML = '';
  });
  
  // Process each product with individual API calls
  csvData.forEach((row, idx) => {
    if (!row.sku) return;
    (function(currentIdx) {
      setTimeout(() => {
        api.getJoliStock(row.sku).then((data) => {
          let el;
          el = document.getElementById('joli-stock-' + currentIdx); 
          if (el) el.innerText = data.stock !== undefined ? data.stock : data.exists ? 'Exists' : 'Not found';
          
          el = document.getElementById('joli-title-' + currentIdx); 
          if (el) el.innerText = data.title || '';
          
          el = document.getElementById('joli-regular-' + currentIdx); 
          if (el) el.innerText = data.regular_price !== undefined ? data.regular_price : '';
          
          el = document.getElementById('joli-sale-' + currentIdx); 
          if (el) el.innerText = data.sale_price !== undefined ? data.sale_price : '';
          
          // Make thumbnail clickable
          el = document.getElementById('joli-thumb-' + currentIdx);
          if (el) {
            // Use the product_url from the API if available
            const productUrl = data.product_url || '';
            if (data.thumbnail) {
              el.innerHTML = `<a href="${productUrl}" target="_blank" data-bs-toggle="tooltip" data-bs-title="Open product page"><img src="${data.thumbnail}" style="max-width:60px;max-height:60px;"></a>`;
              // Initialize tooltip
              const tooltips = el.querySelectorAll('[data-bs-toggle="tooltip"]');
              tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
            } else {
              el.innerHTML = '';
            }
          }
          
          // Update csvData for export
          csvData[currentIdx].joliStock = data.stock !== undefined ? data.stock : '';
          csvData[currentIdx].joliRegular = data.regular_price !== undefined ? data.regular_price : '';
          csvData[currentIdx].joliSale = data.sale_price !== undefined ? data.sale_price : '';
          csvData[currentIdx].title = data.title || '';
          csvData[currentIdx].thumb = data.thumbnail || '';
          csvData[currentIdx].productUrl = data.product_url || '';
        });
      }, currentIdx * 150);
    })(idx);
  });
  showAlert('Fetching all product data...', 'info');
};

window.updateAllJoliStock = function() {
  if (!confirm('Are you sure you want to update ALL selected stocks to Joli?')) return;
  updateResults = [];
  let toUpdate = csvData.map((row, idx) => ({ row, idx })).filter((_, idx) => selectedRows[idx]);
  if (toUpdate.length === 0) { showAlert('No rows selected!', 'warning'); return; }
  const skus = toUpdate.map(({ row }) => row.sku);
  const stocks = toUpdate.map(({ row }) => row.stock);
  showAlert('Bulk updating stock...', 'info', 2000);
  api.updateAllJoliStock(skus, stocks).then((data) => {
    data.forEach((res, i) => {
      updateResults.push({ sku: skus[i], status: res.status || 'error', message: res.message || '' });
    });
    renderResults();
    toUpdate.forEach(({ row, idx }) => getJoliStock(row.sku, idx));
    showAlert('Bulk stock update complete!', 'success');
  }).catch(() => {
    showAlert('Bulk stock update failed!', 'danger');
  });
};

window.updateRegularPriceValue = function(idx, value) { 
  if (!csvData[idx].prices) csvData[idx].prices = {};
  csvData[idx].prices.regular = value; 
};

window.updateSalePriceValue = function(idx, value) { 
  if (!csvData[idx].prices) csvData[idx].prices = {};
  csvData[idx].prices.sale = value; 
};

window.getRegularPriceValue = function(idx) { 
  return csvData[idx].prices?.regular || ''; 
};

window.getSalePriceValue = function(idx) { 
  return csvData[idx].prices?.sale || ''; 
};

window.updateJoliPrice = function(sku, regularPrice, salePrice, idx) {
  updateResults = [];
  showAlert('Updating price...', 'info', 2000);
  api.updateJoliPrice(sku, regularPrice, salePrice).then((data) => {
    updateResults.push({ 
      sku: sku, 
      status: data[0]?.status || 'error', 
      message: data[0]?.message || '' 
    });
    renderResults();
    getJoliStock(sku, idx);
    showAlert('Price updated!', 'success');
  }).catch(() => {
    showAlert('Price update failed!', 'danger');
  });
};

window.updateAllJoliPrices = function() {
  if (!confirm('Are you sure you want to update ALL selected prices to Joli?')) return;
  updateResults = [];
  let toUpdate = csvData.map((row, idx) => ({ row, idx })).filter((_, idx) => selectedRows[idx]);
  if (toUpdate.length === 0) { showAlert('No rows selected!', 'warning'); return; }
  const skus = toUpdate.map(({ row }) => row.sku);
  const regularPrices = toUpdate.map(({ row }) => row.prices?.regular || '');
  const salePrices = toUpdate.map(({ row }) => row.prices?.sale || '');
  showAlert('Bulk updating prices...', 'info', 2000);
  api.updateAllJoliPrices(skus, regularPrices, salePrices).then((data) => {
    data.forEach((res, i) => {
      updateResults.push({ sku: skus[i], status: res.status || 'error', message: res.message || '' });
    });
    renderResults();
    toUpdate.forEach(({ row, idx }) => getJoliStock(row.sku, idx));
    showAlert('Bulk price update complete!', 'success');
  }).catch(() => {
    showAlert('Bulk price update failed!', 'danger');
  });
};

// Clear all product rows
window.clearTable = function() {
  if (csvData.length === 0) {
    showAlert('Table is already empty.', 'info');
    return;
  }
  if (confirm('Are you sure you want to clear all product rows?')) {
    csvData = [];
    selectedRows = [];
    updateResults = [];
    renderTable();
    renderResults();
    showAlert('Table cleared.', 'success');
    document.getElementById('addRowBtn').style.display = '';
  }
};

// CSV/XLSX upload handler for form onsubmit
window.handleCSVUpload = function(event) {
  event.preventDefault();
  const input = event.target.querySelector('input[type="file"]') || document.getElementById('csvFile');
  const file = input && input.files && input.files[0];
  if (!file) return;
  const ext = file.name.split('.').pop().toLowerCase();
  if (ext === 'xlsx') {
    if (typeof XLSX === 'undefined') {
      showAlert('XLSX library not loaded. Please include SheetJS in your HTML.', 'danger');
      return;
    }
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const json = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        if (json.length < 2) {
          showAlert('XLSX file is empty or invalid!', 'danger');
          return;
        }
        // Map columns
        const headers = json[0].map(h => String(h).trim());
        const body = json.slice(1);
        csvData = body.map(rowArr => {
          const rowObj = {};
          headers.forEach((col, idx) => {
            rowObj[col] = rowArr[idx] !== undefined ? rowArr[idx] : '';
          });
          
          // Check for price columns with different possible names
          const regularPrice = 
            rowObj['Regular Price'] || 
            rowObj['Regular'] || 
            rowObj['regular_price'] || 
            rowObj['regular'] || 
            rowObj['CSV/XLSX Regular'] || 
            '';
            
          const salePrice = 
            rowObj['Sale Price'] || 
            rowObj['Sale'] || 
            rowObj['sale_price'] || 
            rowObj['sale'] || 
            rowObj['CSV/XLSX Sale'] || 
            '';
          
          // Map to internal structure
          return {
            sku: rowObj['SKU'] || rowObj['sku'] || '',
            stock: rowObj['Stock'] || rowObj['stock'] || '',
            prices: {
              regular: regularPrice,
              sale: salePrice
            },
            title: rowObj['Title'] || rowObj['title'] || '',
            joliStock: rowObj['Joli Stock'] || rowObj['joliStock'] || '',
            joliRegular: rowObj['Joli Regular'] || rowObj['joliRegular'] || '',
            joliSale: rowObj['Joli Sale'] || rowObj['joliSale'] || '',
            thumb: rowObj['Thumb URL'] || rowObj['thumb'] || rowObj['Thumbnail'] || rowObj['Thumb'] || ''
          };
        });
        selectedRows = Array(csvData.length).fill(true);
        updateResults = [];
        manualMode = false;
        renderTable();
        renderResults();
        showAlert('XLSX imported successfully!', 'success');
        document.getElementById('addRowBtn').style.display = 'none';
      } catch (err) {
        showAlert('XLSX import failed! ' + err.message, 'danger');
      }
    };
    reader.readAsArrayBuffer(file);
  } else if (ext === 'csv') {
    // CSV import (default, backend)
    const formData = new FormData(document.getElementById('uploadForm'));
    loading = true;
    showAlert('Uploading CSV...', 'info', 2000);
    api.uploadCSV(formData).then((data) => {
      csvData = data;
      selectedRows = Array(csvData.length).fill(true);
      updateResults = [];
      manualMode = false;
      renderTable();
      renderResults();
      showAlert('CSV uploaded successfully!', 'success');
      document.getElementById('addRowBtn').style.display = 'none';
      loading = false;
    }).catch(() => {
      showAlert('CSV upload failed!', 'danger');
      loading = false;
    });
  } else {
    showAlert('Unsupported file type!', 'danger');
  }
};

// Function to fetch all Joli data before export
window.fetchAllJoliDataBeforeExport = async function() {
  showAlert('Fetching product data before export...', 'info');
  
  // Create an array of promises for all SKUs that need to be fetched
  const promises = [];
  
  csvData.forEach((row, idx) => {
    if (row.sku && (!row.joliStock || row.joliStock === '')) {
      promises.push(
        api.getJoliStock(row.sku).then(data => {
          // Update csvData for export - handle zero values correctly
          csvData[idx].joliStock = data.stock !== undefined ? data.stock : '';
          csvData[idx].joliRegular = data.regular_price !== undefined ? data.regular_price : '';
          csvData[idx].joliSale = data.sale_price !== undefined ? data.sale_price : '';
          csvData[idx].title = data.title || csvData[idx].title || '';
          csvData[idx].thumb = data.thumbnail || csvData[idx].thumb || '';
          csvData[idx].productUrl = data.product_url || '';
          
          // Update DOM
          document.getElementById('joli-stock-' + idx).innerText = 
            data.stock !== undefined ? data.stock : data.exists ? 'Exists' : 'Not found';
          document.getElementById('joli-title-' + idx).innerText = data.title || '';
          document.getElementById('joli-regular-' + idx).innerText = data.regular_price !== undefined ? data.regular_price : '';
          document.getElementById('joli-sale-' + idx).innerText = data.sale_price !== undefined ? data.sale_price : '';
          
          // Make thumbnail clickable
          const thumbContainer = document.getElementById('joli-thumb-' + idx);
          if (data.thumbnail) {
            const productUrl = data.product_url || '';
            thumbContainer.innerHTML = `<a href="${productUrl}" target="_blank" data-bs-toggle="tooltip" data-bs-title="Open product page"><img src="${data.thumbnail}" style="max-width:60px;max-height:60px;"></a>`;
            // Initialize tooltip
            const tooltips = thumbContainer.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => new bootstrap.Tooltip(tooltip));
          } else {
            thumbContainer.innerHTML = '';
          }
        }).catch(err => {
          console.error(`Error fetching data for SKU ${row.sku}:`, err);
        })
      );
    }
  });
  
  // Wait for all promises to resolve
  if (promises.length > 0) {
    await Promise.all(promises);
    showAlert('Product data fetched successfully!', 'success');
  } else {
    showAlert('All product data already available', 'info');
  }
  
  return true;
};

// Modified export functions to handle zero values correctly
window.exportTableToCSV = async function() {
  if (csvData.length === 0) {
    showAlert('No data to export.', 'warning');
    return;
  }
  
  // Fetch all Joli data first
  await fetchAllJoliDataBeforeExport();
  
  // Get header columns
  const headers = ['SKU', 'Title', 'Joli Stock', 'Joli Regular', 'Joli Sale', 'CSV/XLSX Stock', 'CSV/XLSX Regular', 'CSV/XLSX Sale', 'Thumbnail', 'Product URL'];
  
  // Build CSV rows directly from csvData
  const csvRows = [headers.join(',')];
  
  csvData.forEach(row => {
    const values = [
      `"${(row.sku || '').replace(/"/g, '""')}"`,
      `"${(row.title || '').replace(/"/g, '""')}"`,
      `"${row.joliStock !== undefined && row.joliStock !== '' ? row.joliStock : ''}"`,
      `"${row.joliRegular !== undefined && row.joliRegular !== '' ? row.joliRegular : ''}"`,
      `"${row.joliSale !== undefined && row.joliSale !== '' ? row.joliSale : ''}"`,
      `"${row.stock !== undefined && row.stock !== '' ? row.stock : '0'}"`,
      `"${row.prices?.regular !== undefined && row.prices?.regular !== '' ? row.prices.regular : ''}"`,
      `"${row.prices?.sale !== undefined && row.prices?.sale !== '' ? row.prices.sale : ''}"`,
      `"${(row.thumb || '').replace(/"/g, '""')}"`,
      `"${(row.productUrl || '').replace(/"/g, '""')}"`
    ];
    csvRows.push(values.join(','));
  });
  
  const csvString = csvRows.join('\n');
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', 'products_export.csv');
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  showAlert('CSV exported!', 'success');
};

window.exportTableToXLSX = async function() {
  if (typeof XLSX === 'undefined') {
    showAlert('XLSX library not loaded. Please include SheetJS in your HTML.', 'danger');
    return;
  }
  
  if (csvData.length === 0) {
    showAlert('No data to export.', 'warning');
    return;
  }
  
  // Fetch all Joli data first
  await fetchAllJoliDataBeforeExport();
  
  // Get header columns
  const headers = ['SKU', 'Title', 'Joli Stock', 'Joli Regular', 'Joli Sale', 'CSV/XLSX Stock', 'CSV/XLSX Regular', 'CSV/XLSX Sale', 'Thumbnail', 'Product URL'];
  
  // Build XLSX rows directly from csvData
  const xlsxRows = [headers];
  
  csvData.forEach(row => {
    xlsxRows.push([
      row.sku || '',
      row.title || '',
      row.joliStock !== undefined && row.joliStock !== '' ? row.joliStock : '',
      row.joliRegular !== undefined && row.joliRegular !== '' ? row.joliRegular : '',
      row.joliSale !== undefined && row.joliSale !== '' ? row.joliSale : '',
      row.stock !== undefined && row.stock !== '' ? row.stock : '0',
      row.prices?.regular !== undefined && row.prices?.regular !== '' ? row.prices.regular : '',
      row.prices?.sale !== undefined && row.prices?.sale !== '' ? row.prices.sale : '',
      row.thumb || '',
      row.productUrl || ''
    ]);
  });
  
  const ws = XLSX.utils.aoa_to_sheet(xlsxRows);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, 'Products');
  XLSX.writeFile(wb, 'products_export.xlsx');
  showAlert('XLSX exported!', 'success');
};

// Enable Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function () {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(function (tooltipTriggerEl) {
    new bootstrap.Tooltip(tooltipTriggerEl);
  });
});

// Add a function to show product details in a modal
window.showProductDetailsModal = function(idx) {
  const row = csvData[idx];
  if (!row || !row.sku) return;
  
  const modalTitle = document.getElementById('productDetailsModalLabel');
  const modalBody = document.getElementById('productDetailsBody');
  
  modalTitle.textContent = row.title || `Product: ${row.sku}`;
  
  let content = '<div class="row">';
  
  // Left column with image
  content += '<div class="col-md-4 text-center">';
  if (row.thumb) {
    content += `<img src="${row.thumb}" class="img-fluid mb-2" style="max-height:200px;">`;
  } else {
    content += '<div class="alert alert-secondary">No image available</div>';
  }
  if (row.productUrl) {
    content += `<a href="${row.productUrl}" target="_blank" class="btn btn-sm btn-primary">View on Website</a>`;
  }
  content += '</div>';
  
  // Right column with details
  content += '<div class="col-md-8">';
  content += '<table class="table table-sm">';
  content += `<tr><th>SKU:</th><td>${row.sku || ''}</td></tr>`;
  content += `<tr><th>Title:</th><td>${row.title || ''}</td></tr>`;
  content += `<tr><th>Joli Stock:</th><td>${row.joliStock !== undefined ? row.joliStock : 'Unknown'}</td></tr>`;
  content += `<tr><th>Joli Regular Price:</th><td>${row.joliRegular !== undefined ? row.joliRegular : 'Unknown'}</td></tr>`;
  content += `<tr><th>Joli Sale Price:</th><td>${row.joliSale !== undefined ? row.joliSale : 'Unknown'}</td></tr>`;
  content += `<tr><th>CSV/XLSX Stock:</th><td>${row.stock !== undefined ? row.stock : 'Not set'}</td></tr>`;
  content += `<tr><th>CSV/XLSX Regular Price:</th><td>${row.prices?.regular !== undefined ? row.prices.regular : 'Not set'}</td></tr>`;
  content += `<tr><th>CSV/XLSX Sale Price:</th><td>${row.prices?.sale !== undefined ? row.prices.sale : 'Not set'}</td></tr>`;
  content += '</table>';
  content += '</div>';
  
  content += '</div>';
  
  // Add action buttons
  content += '<div class="mt-3 d-flex justify-content-end gap-2">';
  content += `<button class="btn btn-outline-primary" onclick="getJoliStock('${row.sku}', ${idx}); return false;">Refresh Info</button>`;
  content += `<button class="btn btn-outline-success" onclick="updateJoliStock('${row.sku}', ${row.stock}, ${idx}); return false;">Update Stock</button>`;
  content += `<button class="btn btn-outline-info" onclick="updateJoliPrice('${row.sku}', '${row.prices?.regular || ''}', '${row.prices?.sale || ''}', ${idx}); return false;">Update Price</button>`;
  content += '</div>';
  
  modalBody.innerHTML = content;
  
  // Show the modal
  const modal = new bootstrap.Modal(document.getElementById('productDetailsModal'));
  modal.show();
};

// Column selector function removed - using the one below that works with the HTML modal

// Show/hide empty state message
function toggleEmptyState() {
  const noDataMessage = document.getElementById('noDataMessage');
  if (csvData.length === 0) {
    noDataMessage.classList.remove('d-none');
  } else {
    noDataMessage.classList.add('d-none');
  }
}

// Handle file upload from any file input
window.handleFileUpload = function(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  const formData = new FormData();
  formData.append('csv_file', file);
  
  const fileExt = file.name.split('.').pop().toLowerCase();
  
  if (fileExt === 'csv') {
    loading = true;
    showAlert('Uploading CSV...', 'info', 2000);
    api.uploadCSV(formData).then((data) => {
      csvData = data;
      selectedRows = Array(csvData.length).fill(true);
      updateResults = [];
      manualMode = false;
      renderTable();
      renderResults();
      toggleEmptyState();
      showAlert('CSV uploaded successfully!', 'success');
      loading = false;
    }).catch(() => {
      showAlert('CSV upload failed!', 'danger');
      loading = false;
    });
  } else if (fileExt === 'xlsx') {
    const reader = new FileReader();
    reader.onload = function(e) {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        const json = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        if (json.length < 2) {
          showAlert('XLSX file is empty or invalid!', 'danger');
          return;
        }
        // Map columns
        const headers = json[0].map(h => String(h).trim());
        const body = json.slice(1);
        csvData = body.map(rowArr => {
          const rowObj = {};
          headers.forEach((col, idx) => {
            rowObj[col] = rowArr[idx] !== undefined ? rowArr[idx] : '';
          });
          
          // Check for price columns with different possible names
          const regularPrice = 
            rowObj['Regular Price'] || 
            rowObj['Regular'] || 
            rowObj['regular_price'] || 
            rowObj['regular'] || 
            rowObj['CSV/XLSX Regular'] || 
            '';
            
          const salePrice = 
            rowObj['Sale Price'] || 
            rowObj['Sale'] || 
            rowObj['sale_price'] || 
            rowObj['sale'] || 
            rowObj['CSV/XLSX Sale'] || 
            '';
          
          // Map to internal structure
          return {
            sku: rowObj['SKU'] || rowObj['sku'] || '',
            stock: rowObj['Stock'] || rowObj['stock'] || '',
            prices: {
              regular: regularPrice,
              sale: salePrice
            },
            title: rowObj['Title'] || rowObj['title'] || '',
            joliStock: rowObj['Joli Stock'] || rowObj['joliStock'] || '',
            joliRegular: rowObj['Joli Regular'] || rowObj['joliRegular'] || '',
            joliSale: rowObj['Joli Sale'] || rowObj['joliSale'] || '',
            thumb: rowObj['Thumb URL'] || rowObj['thumb'] || rowObj['Thumbnail'] || rowObj['Thumb'] || ''
          };
        });
        selectedRows = Array(csvData.length).fill(true);
        updateResults = [];
        manualMode = false;
        renderTable();
        renderResults();
        toggleEmptyState();
        showAlert('XLSX imported successfully!', 'success');
      } catch (err) {
        showAlert('XLSX import failed! ' + err.message, 'danger');
      }
    };
    reader.readAsArrayBuffer(file);
  } else {
    showAlert('Unsupported file type!', 'danger');
  }
  
  // Reset file input
  event.target.value = '';
};

// Show paste data modal
window.showPasteDataModal = function() {
  const modal = new bootstrap.Modal(document.getElementById('pasteDataModal'));
  document.getElementById('pasteDataArea').value = '';
  modal.show();
};

// Process pasted data
window.processPastedData = function() {
  const pastedText = document.getElementById('pasteDataArea').value.trim();
  if (!pastedText) {
    showAlert('No data pasted', 'warning');
    return;
  }
  
  // Detect delimiter (tab or comma)
  const firstLine = pastedText.split('\n')[0];
  const delimiter = firstLine.includes('\t') ? '\t' : ',';
  
  // Split by line breaks
  const lines = pastedText.split(/\r\n|\n/);
  if (lines.length < 2) {
    showAlert('Invalid data format', 'danger');
    return;
  }
  
  // Get headers
  const headers = lines[0].split(delimiter).map(h => h.trim().toLowerCase());
  
  // Check if required columns exist
  if (!headers.includes('sku')) {
    showAlert('Data must contain a "sku" column', 'danger');
    return;
  }
  
  // Process data rows
  const newData = [];
  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue;
    
    const values = lines[i].split(delimiter).map(v => v.trim());
    const row = {
      prices: {}
    };
    
    headers.forEach((header, index) => {
      if (values[index] === undefined) return;
      
      if (header === 'sku') row.sku = values[index];
      else if (header === 'stock') row.stock = values[index];
      else if (header.includes('regular')) row.prices.regular = values[index];
      else if (header.includes('sale')) row.prices.sale = values[index];
      else if (header === 'title') row.title = values[index];
    });
    
    if (row.sku) newData.push(row);
  }
  
  if (newData.length === 0) {
    showAlert('No valid data found', 'warning');
    return;
  }
  
  // Add to existing data or replace
  if (csvData.length > 0 && confirm(`Found ${newData.length} products. Add to existing data or replace?\nClick OK to add, Cancel to replace`)) {
    csvData = [...csvData, ...newData];
  } else {
    csvData = newData;
  }
  
  selectedRows = Array(csvData.length).fill(true);
  updateResults = [];
  manualMode = false;
  renderTable();
  renderResults();
  toggleEmptyState();
  
  // Close modal
  bootstrap.Modal.getInstance(document.getElementById('pasteDataModal')).hide();
  
  showAlert(`Imported ${newData.length} products from pasted data`, 'success');
};

// Toggle column selector modal
window.toggleColumnSelector = function() {
  console.log("toggleColumnSelector called");
  console.log("Current visibleColumns:", visibleColumns);

  const modal = new bootstrap.Modal(document.getElementById('columnSelectorModal'));

  // Set current values
  document.getElementById('col-thumbnail').checked = visibleColumns.thumbnail;
  document.getElementById('col-title').checked = visibleColumns.title;
  document.getElementById('col-joliStock').checked = visibleColumns.joliStock;
  document.getElementById('col-regular').checked = visibleColumns.regular;
  document.getElementById('col-sale').checked = visibleColumns.sale;
  document.getElementById('col-stock').checked = visibleColumns.stock;
  document.getElementById('col-actions').checked = visibleColumns.actions;

  console.log("Modal about to show");
  modal.show();
};

// Filter table based on search input
window.filterTable = function() {
  const searchTerm = document.getElementById('searchInput').value.toLowerCase();
  const rows = document.querySelectorAll('#dataTableBody tr');
  
  rows.forEach(row => {
    const sku = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
    const title = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
    
    if (sku.includes(searchTerm) || title.includes(searchTerm)) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
};

// Clear search
window.clearSearch = function() {
  document.getElementById('searchInput').value = '';
  filterTable();
};

// Remove duplicate renderTable function - using the one above that handles column visibility

// Toggle row selection
window.toggleRowSelection = function(idx, checked) {
  selectedRows[idx] = checked;
  renderTable();
};

// Select/deselect all rows
window.selectAllRows = function(checked) {
  selectedRows = Array(csvData.length).fill(checked);
  renderTable();
};

// Deselect all rows
window.deselectAll = function() {
  selectAllRows(false);
};

// Add these functions for price updates
window.updateRegularPriceValue = function(idx, value) {
  if (!csvData[idx].prices) csvData[idx].prices = {};
  csvData[idx].prices.regular = value;
};

window.updateSalePriceValue = function(idx, value) {
  if (!csvData[idx].prices) csvData[idx].prices = {};
  csvData[idx].prices.sale = value;
};

// Enhanced product details modal
window.showProductDetailsModal = function(idx) {
  const row = csvData[idx];
  if (!row || !row.sku) return;
  
  const modalTitle = document.getElementById('productDetailsModalLabel');
  const modalBody = document.getElementById('productDetailsBody');
  
  modalTitle.textContent = row.title || `Product: ${row.sku}`;
  
  let content = '<div class="row">';
  
  // Left column with image
  content += '<div class="col-md-4 text-center">';
  if (row.thumb) {
    content += `<img src="${row.thumb}" class="img-fluid mb-2" style="max-height:200px;">`;
    if (row.productUrl) {
      content += `<div><a href="${row.productUrl}" target="_blank" class="btn btn-sm btn-outline-primary">View Product Page</a></div>`;
    }
  } else {
    content += '<div class="alert alert-secondary">No image available</div>';
  }
  content += '</div>';
  
  // Right column with details
  content += '<div class="col-md-8">';
  content += '<table class="table table-sm">';
  content += `<tr><th>SKU</th><td>${row.sku}</td></tr>`;
  content += `<tr><th>Title</th><td>${row.title || ''}</td></tr>`;
  content += `<tr><th>Joli Stock</th><td>${row.joliStock !== undefined ? row.joliStock : '-'}</td></tr>`;
  content += `<tr><th>Joli Regular Price</th><td>${row.joliRegular !== undefined ? row.joliRegular : '-'}</td></tr>`;
  content += `<tr><th>Joli Sale Price</th><td>${row.joliSale !== undefined ? row.joliSale : '-'}</td></tr>`;
  content += `<tr><th>CSV Stock</th><td><input type="number" id="modal-stock" class="form-control form-control-sm" value="${row.stock || ''}" min="0"></td></tr>`;
  content += `<tr><th>CSV Regular Price</th><td><input type="text" id="modal-regular" class="form-control form-control-sm" value="${row.prices?.regular || ''}"></td></tr>`;
  content += `<tr><th>CSV Sale Price</th><td><input type="text" id="modal-sale" class="form-control form-control-sm" value="${row.prices?.sale || ''}"></td></tr>`;
  content += '</table>';
  content += '</div>';
  content += '</div>';
  
  modalBody.innerHTML = content;
  
  // Set up modal buttons
  document.getElementById('modalRefreshBtn').onclick = function() {
    getJoliStock(row.sku, idx);
    bootstrap.Modal.getInstance(document.getElementById('productDetailsModal')).hide();
  };
  
  document.getElementById('modalUpdateStockBtn').onclick = function() {
    const newStock = document.getElementById('modal-stock').value;
    updateStockValue(idx, newStock);
    updateJoliStock(row.sku, newStock, idx);
    bootstrap.Modal.getInstance(document.getElementById('productDetailsModal')).hide();
  };
  
  document.getElementById('modalUpdatePriceBtn').onclick = function() {
    const newRegular = document.getElementById('modal-regular').value;
    const newSale = document.getElementById('modal-sale').value;
    updateRegularPriceValue(idx, newRegular);
    updateSalePriceValue(idx, newSale);
    updateJoliPrice(row.sku, newRegular, newSale, idx);
    bootstrap.Modal.getInstance(document.getElementById('productDetailsModal')).hide();
  };
  
  const modal = new bootstrap.Modal(document.getElementById('productDetailsModal'));
  modal.show();
};

// Call toggleEmptyState on page load
document.addEventListener('DOMContentLoaded', function() {
  toggleEmptyState();
  
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(function(tooltipTriggerEl) {
    new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Set up file input listeners
  document.getElementById('fileInput').addEventListener('change', handleFileUpload);
  document.getElementById('emptyStateFileInput').addEventListener('change', handleFileUpload);
});
