<?php
require_once("../../../../wp-load.php");


$skroutz_data = file_get_contents("php://input");
$skroutz_data_array = json_decode($skroutz_data, true);

$stack = array();
$stack_var = array();

$event_type = $skroutz_data_array['event_type'];

if ($event_type === 'new_order') {
    $ordered_items = $skroutz_data_array['order']['line_items'];

    foreach ($ordered_items as $item_no => $item_array) {

        $id_parts = explode("-", $item_array['shop_uid']);
        $product_id = $id_parts[0];
        $product_link = get_permalink($product_id);
        if (isset($id_parts[1])) {
            $product = wc_get_product($product_id);
            $variations = $product->get_children();
            foreach ($variations as $variation) {
                $meta = get_post_meta($variation, 'attribute_pa_color', true);
                $term = get_term_by('slug', $meta, 'pa_color');
                if ($id_parts[1] == $term->term_id) {
                    $product_var = wc_get_product($variation);
                    $product_var_sku = get_post_meta($variation, '_sku', true);

                    $previous_stock = get_post_meta($variation, '_stock', true);
                    if ($product_var->get_manage_stock()) {
                        update_post_meta($variation, '_stock', $previous_stock - $item_array['quantity']);
                        if (($previous_stock - $item_array['quantity']) < 1) {


                            $out_of_stock_status = 'outofstock';
                            // 1. Updating the stock quantity
                            update_post_meta($variation, '_stock', 0);

                            // 2. Updating the stock status
                            update_post_meta($variation, '_stock_status', wc_clean($out_of_stock_status));

                            // 3. Updating post term relationship
                            wp_set_post_terms($variation, 'outofstock', 'product_visibility', true);

                            // And finally (optionally if needed)
                            wc_delete_product_transients($variation); // Clear/refresh the variation cache

                            update_post_meta($variation, 'moca-product-availability', 'Εξαντλήθηκε');
                            //update_post_meta($variation, 'onfeed', 'NO');
                        }
                    }

                    $stack_var[$item_no]['id'] = $variation;
                    $stack_var[$item_no]['quantity'] = $item_array['quantity'];
                    $stack_var[$item_no]['image'] = $product_var->get_image(array(75, 75)) . '<br>' . $product_var_sku;
                    $stack_var[$item_no]['title'] = '<a href="' . $product_link . '"  rel="nofollow">' . $product_var->get_title() . ' - ' . $term->name . '(' . $term->term_id . ')</a>';
                    if (is_numeric($previous_stock)) {
                        $stack_var[$item_no]['stock'] = $previous_stock . ' - ' . $item_array['quantity'] . ' = ' . ($previous_stock - $item_array['quantity']);
                    } else {
                        $stack_var[$item_no]['stock'] = '&infin; - ' . $item_array['quantity'] . ' = &infin;';
                    }
                }
            }
        } else {
            $product = wc_get_product($product_id);
            $product_link = get_permalink($product_id);
            $previous_stock = get_post_meta($product_id, '_stock', true);
            if ($product->get_manage_stock()) {
                update_post_meta($product_id, '_stock', $previous_stock - $item_array['quantity']);
                if (($previous_stock - $item_array['quantity']) < 1) {

                    $out_of_stock_status = 'outofstock';
                    // 1. Updating the stock quantity
                    update_post_meta($product_id, '_stock', 0);

                    // 2. Updating the stock status
                    update_post_meta($product_id, '_stock_status', wc_clean($out_of_stock_status));

                    // 3. Updating post term relationship
                    wp_set_post_terms($product_id, 'outofstock', 'product_visibility', true);

                    update_post_meta($product_id, 'moca-product-availability', 'Εξαντλήθηκε');
                    //update_post_meta($product_id, 'onfeed', 'NO');
                }
            }

            $stack[$item_no]['id'] = $product_id;
            $stack[$item_no]['quantity'] = $item_array['quantity'];
            $product_sku = $product->get_sku();
            $stack[$item_no]['image'] = $product->get_image(array(75, 75)) . '<br>' . $product_sku;
            $stack[$item_no]['title'] = '<a href="' . $product_link . '"  rel="nofollow">' . $product->get_title() . '</a>';



            if (is_numeric($previous_stock)) {
                $stack[$item_no]['stock'] = $previous_stock . ' - ' . $item_array['quantity'] . ' = ' . ($previous_stock - $item_array['quantity']);
            } else {
                $stack[$item_no]['stock'] = '&infin; - ' . $item_array['quantity'] . ' = &infin;';
            }
        }
    }
    $merged_arrays = array_merge($stack, $stack_var);



    wp_mail('<EMAIL>', 'To Skroutz Marketplace άλλαξε αποθέματα στο Joli', array_to_table($merged_arrays, $event_type, $skroutz_data_array['order']['code'], $skroutz_data_array['order']['courier'], $skroutz_data_array['order']['courier_voucher']), array('Content-Type: text/html; charset=UTF-8'));

    //add_skroutz_marketplace_order_to_orders($skroutz_data_array['order']['customer'], $merged_arrays); /*An einai energo prepei na apenergopoiithoun  2 kai 2 grammes pou kanoun update to stock*/

} elseif ($event_type === 'order_updated') {
    if (isset($skroutz_data_array['order']['state']) && $skroutz_data_array['order']['state'] === 'accepted') {
        if (
            isset($skroutz_data_array['changes']['state']) &&
            isset($skroutz_data_array['changes']['state']['old']) &&
            isset($skroutz_data_array['changes']['state']['new']) &&
            $skroutz_data_array['changes']['state']['old'] !== $skroutz_data_array['changes']['state']['new'] &&
            $skroutz_data_array['changes']['state']['new'] === 'accepted'
        ) {

            /*-----------METAFRAZEI TO VOUCHER SE TEXT OSTE NA DOUME TO THL TOY PARALIPTI TOULAXISTON AFOU EXOUME APODEXTEI TIN PARAGGELIA -------------*/
            /*
            require "pdfcrowd.php";

            try {
                // create the API client instance
                $client = new \Pdfcrowd\PdfToTextClient("demo", "ce544b6ea52a5621fb9d55f8b542d14d");

                // run the conversion and store the result into the "txt" variable
                $pdfhtmldemo = $client->convertUrl($skroutz_data_array['order']['courier_voucher']);

                $pdfhtml = str_replace("-DEMO-", "", $pdfhtmldemo);

                wp_mail(
                    '<EMAIL>',
                    'Αποδεχτείκατε την παραγγελία ' . $skroutz_data_array['order']['code'],
                    $skroutz_data_array['order']['courier'] . ' <a href="' . $skroutz_data_array['order']['courier_voucher'] . '">Voucher link</a><br><br><br>Voucher Text Content<hr>' . $pdfhtml . '<hr>',
                    array('Content-Type: text/html; charset=UTF-8')
                );

                // at this point the "txt" variable contains TXT raw data and
                // can be sent in an HTTP response, saved to a file, etc.
            } catch (\Pdfcrowd\Error $why) {
                // report the error
                error_log("Pdfcrowd Error: {$why}\n");

                // rethrow or handle the exception
                throw $why;
            }*/
        }
        /*-----------METAFRAZEI TO VOUCHER SE TEXT OSTE NA DOUME TO THL TOY PARALIPTI TOULAXISTON AFOU EXOUME APODEXTEI TIN PARAGGELIA END-------------*/
    } //elseif ($skroutz_data_array['order']['state'] === 'rejected' || $skroutz_data_array['order']['state'] === 'cancelled' || $skroutz_data_array['order']['state'] === 'expired') {
    //     /*----EPISTREFEI TA APOTHEMATA OTAN I PARAGGELIA AKYROTHEI ---- */
    //     if (
    //         $skroutz_data_array['changes']['state']['old'] !== $skroutz_data_array['changes']['state']['new'] && $skroutz_data_array['changes']['state']['new'] === 'cancelled'
    //     ) {
    //         $ordered_items = $skroutz_data_array['order']['line_items'];

    //         foreach ($ordered_items as $item_no => $item_array) {

    //             $id_parts = explode("-", $item_array['shop_uid']);
    //             $product_id = $id_parts[0];
    //             $product_link = get_permalink($product_id);
    //             if ($id_parts[1]) {
    //                 $product = wc_get_product($product_id);
    //                 $variations = $product->get_children();
    //                 foreach ($variations as $variation) {
    //                     $meta = get_post_meta($variation, 'attribute_pa_color', true);
    //                     $term = get_term_by('slug', $meta, 'pa_color');
    //                     if ($id_parts[1] == $term->term_id) {
    //                         $product_var = wc_get_product($variation);
    //                         $product_var_sku = get_post_meta($variation, '_sku', true);

    //                         $previous_stock = get_post_meta($variation, '_stock', true);
    //                         if ($product_var->get_manage_stock()) {
    //                             if (($previous_stock + $item_array['quantity']) > 0) {

    //                                 $in_stock_status = 'instock';
    //                                 // 1. Updating the stock quantity
    //                                 update_post_meta($variation, '_stock', $previous_stock + $item_array['quantity']);

    //                                 // 2. Updating the stock status
    //                                 update_post_meta($variation, '_stock_status', wc_clean($in_stock_status));

    //                                 // 3. Updating post term relationship.PROSOXI OTAN EINAI OUTOFSTOCK THELEI KAI ALLI PARAMENTRO                                                                    
    //                                 wp_set_post_terms($variation, 'instock', 'product_visibility');

    //                                 // And finally (optionally if needed)
    //                                 wc_delete_product_transients($variation); // Clear/refresh the variation cache

    //                                 update_post_meta($variation, 'moca-product-availability', 'Άμεσα διαθέσιμο');
    //                                 //update_post_meta($variation, 'onfeed', 'NO');
    //                             }
    //                         }

    //                         $stack_var[$item_no]['id'] = $variation;
    //                         $stack_var[$item_no]['quantity'] = $item_array['quantity'];
    //                         $stack_var[$item_no]['image'] = $product_var->get_image(array(75, 75)) . '<br>' . $product_var_sku;
    //                         $stack_var[$item_no]['title'] = '<a href="' . $product_link . '"  rel="nofollow">' . $product_var->get_title() . ' - ' . $term->name . '(' . $term->term_id . ')</a>';
    //                         if (is_numeric($previous_stock)) {
    //                             $stack_var[$item_no]['stock'] = $previous_stock . ' + ' . $item_array['quantity'] . ' = ' . ($previous_stock + $item_array['quantity']);
    //                         } else {
    //                             $stack_var[$item_no]['stock'] = '&infin; + ' . $item_array['quantity'] . ' = &infin;';
    //                         }
    //                     }
    //                 }
    //             } else {
    //                 $product = wc_get_product($product_id);
    //                 $product_link = get_permalink($product_id);
    //                 $previous_stock = get_post_meta($product_id, '_stock', true);
    //                 if ($product->get_manage_stock()) {
    //                     if (($previous_stock + $item_array['quantity']) > 0) {

    //                         $in_stock_status = 'instock';
    //                         // 1. Updating the stock quantity
    //                         update_post_meta($product_id, '_stock', $previous_stock + $item_array['quantity']);

    //                         // 2. Updating the stock status
    //                         update_post_meta($product_id, '_stock_status', wc_clean($in_stock_status));

    //                         // 3. Updating post term relationship.PROSOXI OTAN EINAI OUTOFSTOCK THELEI KAI ALLI PARAMENTRO   
    //                         wp_set_post_terms($product_id, 'instock', 'product_visibility');

    //                         update_post_meta($product_id, 'moca-product-availability', 'Άμεσα διαθέσιμο');
    //                         //update_post_meta($product_id, 'onfeed', 'NO');
    //                     }
    //                 }

    //                 $stack[$item_no]['id'] = $product_id;
    //                 $stack[$item_no]['quantity'] = $item_array['quantity'];
    //                 $product_sku = $product->get_sku();
    //                 $stack[$item_no]['image'] = $product->get_image(array(75, 75)) . '<br>' . $product_sku;
    //                 $stack[$item_no]['title'] = '<a href="' . $product_link . '"  rel="nofollow">' . $product->get_title() . '</a>';



    //                 if (is_numeric($previous_stock)) {
    //                     $stack[$item_no]['stock'] = $previous_stock . ' + ' . $item_array['quantity'] . ' = ' . ($previous_stock + $item_array['quantity']);
    //                 } else {
    //                     $stack[$item_no]['stock'] = '&infin; + ' . $item_array['quantity'] . ' = &infin;';
    //                 }
    //             }
    //         }
    //         $merged_arrays = array_merge($stack, $stack_var);



    //         wp_mail('<EMAIL>', 'To Skroutz Marketplace άλλαξε αποθέματα στο Joli - ' . $skroutz_data_array['order']['state'], array_to_table($merged_arrays, $event_type, $skroutz_data_array['order']['code'], $skroutz_data_array['order']['courier'], $skroutz_data_array['order']['courier_voucher']), array('Content-Type: text/html; charset=UTF-8'));


    //         /*TESTING */


    //         $gk_msg = '<pre>' . print_r($skroutz_data_array, true) . '</pre>';

    //         wp_mail('<EMAIL>', 'Skroutz changes', $gk_msg, array('Content-Type: text/html; charset=UTF-8'));
    //     }


    //     /*TESTING END*/
    // }
    /*----EPISTREFEI TA APOTHEMATA OTAN I PARAGGELIA AKYROTHEI END---- */
}

function array_to_table($merged_arrays, $event_type, $order_code, $order_courier, $order_voucher)
{
    ob_start();

    echo ' <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Document</title>
            <style>
                table {border-collapse: collapse;font-size: 0.95em;}
                tr {border-bottom:1px solid #eee;}
                th, td {padding: 0.5em;}
            </style>
        </head>
        <body><h3>' . $event_type . ' - ' . $order_code . '</h3><table>';

    // Table header
    foreach ($merged_arrays[0] as $clave => $fila) {
        echo "<th>" . $clave . "</th>";
    }

    // Table body
    foreach ($merged_arrays as $fila) {
        echo "<tr>";
        foreach ($fila as $elemento) {
            echo "<td>" . $elemento . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>
    <p>" . $order_courier . " <a href='" . $order_voucher . "'>" . $order_code . ".pdf</a></p>
    </body>
    </html> ";
    return ob_get_clean();
}




function add_skroutz_marketplace_order_to_orders($customer_details, $merged_arrays)
{
    $order = wc_create_order();

    $pysData = [];
    $pysData['pys_source'] = sanitize_text_field('marketplace.skroutz.gr');
    $order->update_meta_data("pys_enrich_data", $pysData);



    // add products
    foreach ($merged_arrays as $key => $value) {
        $product_id = $merged_arrays[$key]['id'];
        $product_quantity = $merged_arrays[$key]['quantity'];
        $order->add_product(wc_get_product($product_id), $product_quantity);
    }


    // add shipping
    /* 
    $shipping = new WC_Order_Item_Shipping();
        $shipping->set_method_title( 'Free shipping' );
        $shipping->set_method_id( 'free_shipping:1' ); // set an existing Shipping method ID
        $shipping->set_total( 0 ); // optional
        $order->add_item( $shipping );
        */

    // add billing and shipping addresses
    $address = array(
        'first_name' => $customer_details['first_name'],
        'last_name'  => $customer_details['last_name'],
        'company'    => '',
        'email'      => '',
        'phone'      => '',
        'address_1'  => $customer_details['address']['street_name'] . ' ' . $customer_details['address']['street_number'],
        'address_2'  => '',
        'city'       => $customer_details['address']['city'],
        'state'      => $customer_details['address']['region'],
        'postcode'   => $customer_details['address']['zip'],
        'country'    => $customer_details['address']['country_code']
    );

    $order->set_address($address, 'billing');
    $order->set_address($address, 'shipping');

    // add payment method
    /*$order->set_payment_method( 'stripe' );
        $order->set_payment_method_title( 'Credit/Debit card' );*/

    // order status
    $order->set_status('wc-completed', 'Skroutz Marketplace Order');

    // calculate and save
    $order->calculate_totals();
    $order->save();
}
