<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Stock & Price Update</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/styles.css" />
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script type="module" src="js/app.js"></script>
  </head>
  <body>
    <div class="container-fluid py-4">
      <div class="row mb-3">
        <div class="col-lg-10 col-md-9">
          <h1 class="mb-0">Stock & Price Update Tool</h1>
        </div>
        <div class="col-lg-2 col-md-3 text-end">
          <div class="btn-group">
            <button class="btn btn-outline-secondary" onclick="toggleColumnSelector()">
              <i class="bi bi-layout-three-columns"></i> Columns
            </button>
            <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
              <i class="bi bi-question-circle"></i> Help
            </button>
          </div>
        </div>
      </div>
      
      <div id="alertArea" class="sticky-top pt-2 pb-2 bg-white" style="top: 0; z-index: 1020;"></div>

      <!-- Main Toolbar -->
      <div class="card mb-3">
        <div class="card-body">
          <div class="row g-2">
            <div class="col-md-6">
              <div class="d-flex flex-wrap gap-2">
                <button id="addRowBtn" class="btn btn-outline-primary" onclick="addManualRow()">
                  <i class="bi bi-plus-lg"></i> Add Row
                </button>
                <div class="btn-group">
                  <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-upload"></i> Import
                  </button>
                  <ul class="dropdown-menu">
                    <li>
                      <label class="dropdown-item" style="cursor: pointer;">
                        <input type="file" id="fileInput" accept=".csv,.xlsx" style="display: none;" onchange="handleFileUpload(event)">
                        <i class="bi bi-file-earmark-spreadsheet"></i> Upload CSV/XLSX
                      </label>
                    </li>
                    <li><a class="dropdown-item" href="#" onclick="showPasteDataModal()"><i class="bi bi-clipboard"></i> Paste Data</a></li>
                  </ul>
                </div>
                <div class="btn-group">
                  <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-download"></i> Export
                  </button>
                  <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportTableToCSV()"><i class="bi bi-filetype-csv"></i> Export as CSV</a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportTableToXLSX()"><i class="bi bi-file-earmark-excel"></i> Export as XLSX</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="col-md-6 text-end">
              <div class="d-flex flex-wrap gap-2 justify-content-end">
                <div class="input-group" style="max-width: 300px;">
                  <input type="text" class="form-control" id="searchInput" placeholder="Search SKU or title..." onkeyup="filterTable()">
                  <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                    <i class="bi bi-x"></i>
                  </button>
                </div>
                <button class="btn btn-outline-danger" onclick="clearTable()">
                  <i class="bi bi-trash"></i> Clear Table
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Floating Bulk Toolbar -->
      <div id="bulkToolbar" class="fixed-bottom bg-light border-top shadow-sm py-2 px-3 d-none" style="z-index:1050;">
        <div class="container-fluid">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="badge bg-primary" id="selectedCount">0 selected</span>
            </div>
            <div class="col text-center">
              <div class="btn-group">
                <button class="btn btn-outline-primary" onclick="getAllJoliStock()">
                  <i class="bi bi-cloud-download"></i> Get Info
                </button>
                <button class="btn btn-outline-success" onclick="updateAllJoliStock()">
                  <i class="bi bi-box-seam"></i> Update Stock
                </button>
                <button class="btn btn-outline-info" onclick="updateAllJoliPrices()">
                  <i class="bi bi-tag"></i> Update Prices
                </button>
              </div>
            </div>
            <div class="col-auto">
              <button class="btn btn-sm btn-outline-secondary" onclick="deselectAll()">
                <i class="bi bi-x-lg"></i> Cancel
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle" id="dataTable">
          <thead class="table-light sticky-top" style="top: 0; z-index: 1010;">
            <tr>
              <th style="width:40px;">
                <input type="checkbox" class="form-check-input" id="select-all" checked onchange="selectAllRows(this.checked)" />
              </th>
              <th>Thumbnail</th>
              <th>SKU</th>
              <th>Title</th>
              <th class="bg-joli">Joli Stock</th>
              <th class="bg-joli">Joli Regular</th>
              <th class="bg-joli">Joli Sale</th>
              <th class="bg-csv">CSV Stock</th>
              <th class="bg-csv">CSV Regular</th>
              <th class="bg-csv">CSV Sale</th>
              <th style="width:100px;">Actions</th>
            </tr>
          </thead>
          <tbody id="dataTableBody">
            <!-- Data will be populated here -->
          </tbody>
        </table>
      </div>
      
      <div id="noDataMessage" class="text-center py-5 d-none">
        <div class="display-1 text-muted mb-3"><i class="bi bi-table"></i></div>
        <h3 class="text-muted">No Data Available</h3>
        <p class="text-muted">Upload a CSV/XLSX file or add rows manually to get started</p>
        <div class="mt-3">
          <button class="btn btn-primary me-2" onclick="showPasteDataModal()">
            <i class="bi bi-clipboard"></i> Paste Data
          </button>
          <label class="btn btn-outline-primary">
            <input type="file" id="emptyStateFileInput" accept=".csv,.xlsx" style="display: none;" onchange="handleFileUpload(event)">
            <i class="bi bi-upload"></i> Upload File
          </label>
        </div>
      </div>
      
      <div id="updateResults" class="result-list mt-4"></div>

      <!-- Product Details Modal -->
      <div class="modal fade" id="productDetailsModal" tabindex="-1" aria-labelledby="productDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="productDetailsModalLabel">Product Details</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="productDetailsBody">
              <!-- Filled by JS -->
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <div class="btn-group">
                <button type="button" class="btn btn-primary" id="modalRefreshBtn">Refresh Info</button>
                <button type="button" class="btn btn-success" id="modalUpdateStockBtn">Update Stock</button>
                <button type="button" class="btn btn-info" id="modalUpdatePriceBtn">Update Price</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Paste Data Modal -->
      <div class="modal fade" id="pasteDataModal" tabindex="-1" aria-labelledby="pasteDataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="pasteDataModalLabel">Paste Data</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <p class="text-muted">Paste data from Excel or CSV (tab or comma separated). First row should contain headers.</p>
              <textarea id="pasteDataArea" class="form-control" rows="10" placeholder="Paste your data here..."></textarea>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" onclick="processPastedData()">Import Data</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Help Modal -->
      <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="helpModalLabel">How to Use</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                      <i class="bi bi-1-circle"></i> Import Data
                    </div>
                    <div class="card-body">
                      <p>Upload a CSV/XLSX file with columns:</p>
                      <ul>
                        <li><strong>sku</strong> (required)</li>
                        <li><strong>stock</strong> (optional)</li>
                        <li><strong>regular_price</strong> (optional)</li>
                        <li><strong>sale_price</strong> (optional)</li>
                      </ul>
                      <p>Or paste data directly from Excel.</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                      <i class="bi bi-2-circle"></i> Manage Products
                    </div>
                    <div class="card-body">
                      <p>Use the actions menu for individual products:</p>
                      <ul>
                        <li>Get product info from store</li>
                        <li>Update stock quantities</li>
                        <li>Update product prices</li>
                        <li>View detailed product information</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                      <i class="bi bi-3-circle"></i> Bulk Operations
                    </div>
                    <div class="card-body">
                      <p>Select multiple rows to enable bulk actions:</p>
                      <ul>
                        <li>Get info for all selected products</li>
                        <li>Update stock for all selected products</li>
                        <li>Update prices for all selected products</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header bg-warning text-dark">
                      <i class="bi bi-4-circle"></i> Export Data
                    </div>
                    <div class="card-body">
                      <p>Export your data in different formats:</p>
                      <ul>
                        <li>CSV format for compatibility</li>
                        <li>XLSX format for Excel</li>
                      </ul>
                      <p>All current data will be included in exports.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Column Selector Modal -->
      <div class="modal fade" id="columnSelectorModal" tabindex="-1" aria-labelledby="columnSelectorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="columnSelectorModalLabel">Select Visible Columns</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <form id="columnSelectorForm">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-thumbnail" name="col-thumbnail" checked>
                      <label class="form-check-label" for="col-thumbnail">Thumbnail</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-title" name="col-title" checked>
                      <label class="form-check-label" for="col-title">Title</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-joliStock" name="col-joliStock" checked>
                      <label class="form-check-label" for="col-joliStock">Joli Stock</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-regular" name="col-regular" checked>
                      <label class="form-check-label" for="col-regular">Regular Price</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-sale" name="col-sale" checked>
                      <label class="form-check-label" for="col-sale">Sale Price</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-stock" name="col-stock" checked>
                      <label class="form-check-label" for="col-stock">CSV Stock</label>
                    </div>
                    <div class="form-check mb-2">
                      <input class="form-check-input" type="checkbox" id="col-actions" name="col-actions" checked>
                      <label class="form-check-label" for="col-actions">Actions</label>
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" onclick="applyColumnSelection()" data-bs-dismiss="modal">Apply</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
