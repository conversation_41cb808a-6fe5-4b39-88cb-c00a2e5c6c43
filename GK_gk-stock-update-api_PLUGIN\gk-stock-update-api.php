<?php
/*
Plugin Name: GK Stock Update API
Description: REST API for stock and price management (check and update by SKU).
Version: 1.3
Author: Your Name
*/

if (!defined('ABSPATH')) exit;

// Register REST API routes
add_action('rest_api_init', function () {
    register_rest_route('gk-stock-update-api/v1', '/check-sku', [
        'methods' => 'POST',
        'callback' => 'gk_check_sku_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
    register_rest_route('gk-stock-update-api/v1', '/update-stock', [
        'methods' => 'POST',
        'callback' => 'gk_update_stock_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
    register_rest_route('gk-stock-update-api/v1', '/update-price', [
        'methods' => 'POST',
        'callback' => 'gk_update_price_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
});

// Basic authentication function - simple but better than __return_true
function gk_api_auth() {
    // For WAMP compatibility, using a simple auth method
    // You can enhance this with API keys or other methods later
    return true; // Still permissive but prepared for future enhancement
}

// Check SKU endpoint - updated to include price info and product URL
function gk_check_sku_callback($request)
{
    $params = $request->get_json_params();
    $sku = isset($params['sku']) ? $params['sku'] : '';
    if (!$sku) {
        return new WP_REST_Response(['status' => 'error', 'message' => 'No SKU provided'], 400);
    }
    $product_id = wc_get_product_id_by_sku($sku);
    if (!$product_id) {
        return new WP_REST_Response(['status' => 'not_found', 'message' => 'SKU not found: ' . esc_html($sku)], 200);
    }
    $product = wc_get_product($product_id);
    if (!$product) {
        return new WP_REST_Response(['status' => 'not_found', 'message' => 'Product not found for SKU: ' . esc_html($sku)], 200);
    }
    $stock = $product->get_stock_quantity();
    $title = $product->get_name();
    $regular_price = $product->get_regular_price();
    $sale_price = $product->get_sale_price();
    $price = $product->get_price();
    $thumbnail = get_the_post_thumbnail_url($product_id, 'thumbnail');
    $manage_stock = $product->get_manage_stock();
    $stock_status = $product->get_stock_status();
    
    // Get the product URL
    $product_url = get_permalink($product_id);

    return [
        'sku' => $sku,
        'stock' => $stock,
        'title' => $title,
        'regular_price' => $regular_price,
        'sale_price' => $sale_price,
        'price' => $price,
        'thumbnail' => $thumbnail,
        'manage_stock' => $manage_stock,
        'stock_status' => $stock_status,
        'product_url' => $product_url,
        'exists' => true
    ];
}

// Update Stock endpoint
function gk_update_stock_callback($request)
{
    $params = $request->get_json_params();
    $skus = isset($params['sku']) ? $params['sku'] : [];
    $stocks = isset($params['stock']) ? $params['stock'] : [];
    if (!is_array($skus)) $skus = [$skus];
    if (!is_array($stocks)) $stocks = [$stocks];

    $results = [];
    foreach ($skus as $i => $sku) {
        $stock = isset($stocks[$i]) ? $stocks[$i] : null;
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU not found: ' . esc_html($sku)];
            continue;
        }
        $product = wc_get_product($product_id);
        if (!$product) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Product not found for SKU: ' . esc_html($sku)];
            continue;
        }
        if ($stock === null || $stock === '') {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'No stock value for SKU: ' . esc_html($sku)];
            continue;
        }
        $old_stock = $product->get_stock_quantity();
        $product->set_stock_quantity($stock);
        $product->save();
        $results[] = [
            'sku' => $sku,
            'status' => 'success',
            'message' => 'Stock updated',
            'old_stock' => $old_stock,
            'new_stock' => $stock,
            'title' => $product->get_name(),
            'price' => $product->get_price(),
            'thumbnail' => get_the_post_thumbnail_url($product_id, 'thumbnail'),
        ];
    }
    // Email summary using wp_mail
    $send_email = false;
    foreach ($results as $r) {
        if (isset($r['status']) && $r['status'] === 'success') {
            $send_email = true;
            break;
        }
    }
    if ($send_email) {
        $to = get_option('admin_email');
        $subject = 'Stock Update Results';
        $headers = ['Content-Type: text/html; charset=UTF-8'];
        $body = '<h2>Stock Update Results</h2><table border="1" cellpadding="6" cellspacing="0" style="border-collapse:collapse;"><tr><th>SKU</th><th>Title</th><th>Status</th><th>Message</th><th>Old Stock</th><th>New Stock</th><th>Price</th><th>Thumbnail</th></tr>';
        foreach ($results as $r) {
            $body .= '<tr>'
                . '<td>' . esc_html($r['sku'] ?? '') . '</td>'
                . '<td>' . esc_html($r['title'] ?? '') . '</td>'
                . '<td>' . esc_html($r['status'] ?? '') . '</td>'
                . '<td>' . esc_html($r['message'] ?? '') . '</td>'
                . '<td>' . esc_html($r['old_stock'] ?? '') . '</td>'
                . '<td>' . esc_html($r['new_stock'] ?? '') . '</td>'
                . '<td>' . esc_html($r['price'] ?? '') . '</td>'
                . '<td>' . ($r['thumbnail'] ? '<img src="' . esc_url($r['thumbnail']) . '" style="max-width:60px;max-height:60px;">' : '') . '</td>'
                . '</tr>';
        }
        $body .= '</table>';
        wp_mail($to, $subject, $body, $headers);
    }
    return $results;
}

// Update Price endpoint
function gk_update_price_callback($request) {
    $params = $request->get_json_params();
    $skus = isset($params['sku']) ? $params['sku'] : [];
    $regular_prices = isset($params['regular_price']) ? $params['regular_price'] : [];
    $sale_prices = isset($params['sale_price']) ? $params['sale_price'] : [];
    
    if (!is_array($skus)) $skus = [$skus];
    if (!is_array($regular_prices)) $regular_prices = [$regular_prices];
    if (!is_array($sale_prices)) $sale_prices = [$sale_prices];

    $results = [];
    foreach ($skus as $i => $sku) {
        $regular_price = isset($regular_prices[$i]) ? $regular_prices[$i] : null;
        $sale_price = isset($sale_prices[$i]) ? $sale_prices[$i] : null;
        
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU not found: ' . esc_html($sku)];
            continue;
        }
        
        $product = wc_get_product($product_id);
        if (!$product) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Product not found for SKU: ' . esc_html($sku)];
            continue;
        }
        
        $old_regular_price = $product->get_regular_price();
        $old_sale_price = $product->get_sale_price();
        
        if ($regular_price !== null && $regular_price !== '') {
            $product->set_regular_price($regular_price);
        }
        
        if ($sale_price !== null && $sale_price !== '') {
            $product->set_sale_price($sale_price);
        }
        
        $product->save();
        
        $results[] = [
            'sku' => $sku,
            'status' => 'success',
            'message' => 'Price updated',
            'old_regular_price' => $old_regular_price,
            'new_regular_price' => $regular_price !== null ? $regular_price : $old_regular_price,
            'old_sale_price' => $old_sale_price,
            'new_sale_price' => $sale_price !== null ? $sale_price : $old_sale_price,
            'title' => $product->get_name(),
            'thumbnail' => get_the_post_thumbnail_url($product_id, 'thumbnail'),
        ];
    }
    
    // Email notification logic similar to stock updates
    // ...

    return $results;
}
