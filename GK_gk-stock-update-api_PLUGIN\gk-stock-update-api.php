<?php
/*
Plugin Name: GK Stock Update API
Description: REST API for stock and price management (check and update by SKU).
Version: 1.3
Author: Your Name
*/

if (!defined('ABSPATH')) exit;

// Register REST API routes
add_action('rest_api_init', function () {
    register_rest_route('gk-stock-update-api/v1', '/check-sku', [
        'methods' => 'POST',
        'callback' => 'gk_check_sku_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
    register_rest_route('gk-stock-update-api/v1', '/update-stock', [
        'methods' => 'POST',
        'callback' => 'gk_update_stock_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
    register_rest_route('gk-stock-update-api/v1', '/update-price', [
        'methods' => 'POST',
        'callback' => 'gk_update_price_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
});

// Basic authentication function - simple but better than __return_true
function gk_api_auth() {
    // For WAMP compatibility, using a simple auth method
    // You can enhance this with API keys or other methods later
    return true; // Still permissive but prepared for future enhancement
}

// Check SKU endpoint - updated to include price info and product URL
function gk_check_sku_callback($request)
{
    $params = $request->get_json_params();
    $sku = isset($params['sku']) ? $params['sku'] : '';
    if (!$sku) {
        return new WP_REST_Response(['status' => 'error', 'message' => 'No SKU provided'], 400);
    }
    $product_id = wc_get_product_id_by_sku($sku);
    if (!$product_id) {
        return new WP_REST_Response(['status' => 'not_found', 'message' => 'SKU not found: ' . esc_html($sku)], 200);
    }
    $product = wc_get_product($product_id);
    if (!$product) {
        return new WP_REST_Response(['status' => 'not_found', 'message' => 'Product not found for SKU: ' . esc_html($sku)], 200);
    }
    $stock = $product->get_stock_quantity();
    $title = $product->get_name();
    $regular_price = $product->get_regular_price();
    $sale_price = $product->get_sale_price();
    $price = $product->get_price();
    $thumbnail = get_the_post_thumbnail_url($product_id, 'thumbnail');
    $manage_stock = $product->get_manage_stock();
    $stock_status = $product->get_stock_status();
    
    // Get the product URL
    $product_url = get_permalink($product_id);

    return [
        'sku' => $sku,
        'stock' => $stock,
        'title' => $title,
        'regular_price' => $regular_price,
        'sale_price' => $sale_price,
        'price' => $price,
        'thumbnail' => $thumbnail,
        'manage_stock' => $manage_stock,
        'stock_status' => $stock_status,
        'product_url' => $product_url,
        'exists' => true
    ];
}

// Update Stock endpoint
function gk_update_stock_callback($request)
{
    $params = $request->get_json_params();
    $skus = isset($params['sku']) ? $params['sku'] : [];
    $stocks = isset($params['stock']) ? $params['stock'] : [];
    if (!is_array($skus)) $skus = [$skus];
    if (!is_array($stocks)) $stocks = [$stocks];

    $results = [];
    $products_to_clear_cache = []; // Collect products for bulk cache clearing

    foreach ($skus as $i => $sku) {
        $stock = isset($stocks[$i]) ? $stocks[$i] : null;

        // a. Exact SKU match only - find product by exact SKU
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU not found: ' . esc_html($sku)];
            continue;
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Product not found for SKU: ' . esc_html($sku)];
            continue;
        }

        // Verify exact SKU match (not partial)
        if ($product->get_sku() !== $sku) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU mismatch - found: ' . esc_html($product->get_sku())];
            continue;
        }

        // b. If stock value is empty, skip (keep current value)
        if ($stock === null || $stock === '') {
            $results[] = ['sku' => $sku, 'status' => 'skipped', 'message' => 'Empty stock value - keeping current value'];
            continue;
        }

        // d. Sanitize and validate stock value to prevent database issues
        $stock = intval($stock);
        if ($stock < 0) {
            $stock = 0; // Prevent negative stock
        }

        $old_stock = $product->get_stock_quantity();

        // Handle both simple and variable products
        if ($product->is_type('variable')) {
            // For variable products, we need to find the exact variation by SKU
            $variations = $product->get_children();
            $variation_updated = false;

            foreach ($variations as $variation_id) {
                $variation = wc_get_product($variation_id);
                if ($variation && $variation->get_sku() === $sku) {
                    $old_variation_stock = $variation->get_stock_quantity();

                    // Update stock quantity
                    $variation->set_stock_quantity($stock);

                    // Handle moca-product-availability and stock status based on stock transition
                    if ($old_variation_stock > 0 && $stock == 0) {
                        // Positive to 0: Set out of stock
                        $variation->set_stock_status('outofstock');
                        update_post_meta($variation_id, '_stock_status', 'outofstock');
                        wp_set_post_terms($variation_id, 'outofstock', 'product_visibility', true);
                        update_post_meta($variation_id, 'moca-product-availability', 'Εξαντλήθηκε');
                    } elseif (($old_variation_stock == 0 || $old_variation_stock === null) && $stock > 0) {
                        // 0 to positive: Set in stock
                        $variation->set_stock_status('instock');
                        update_post_meta($variation_id, '_stock_status', 'instock');
                        wp_set_post_terms($variation_id, 'instock', 'product_visibility');
                        update_post_meta($variation_id, 'moca-product-availability', 'Άμεσα διαθέσιμο');
                    }

                    $variation->save();
                    $products_to_clear_cache[] = $variation_id;
                    $variation_updated = true;

                    $results[] = [
                        'sku' => $sku,
                        'status' => 'success',
                        'message' => 'Variation stock updated',
                        'old_stock' => $old_variation_stock,
                        'new_stock' => $stock,
                        'title' => $variation->get_name(),
                        'price' => $variation->get_price(),
                        'thumbnail' => get_the_post_thumbnail_url($variation_id, 'thumbnail'),
                    ];
                    break;
                }
            }

            if (!$variation_updated) {
                $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Variation with exact SKU not found'];
            }
        } else {
            // Simple product
            $product->set_stock_quantity($stock);

            // Handle moca-product-availability and stock status based on stock transition
            if ($old_stock > 0 && $stock == 0) {
                // Positive to 0: Set out of stock
                $product->set_stock_status('outofstock');
                update_post_meta($product_id, '_stock_status', 'outofstock');
                wp_set_post_terms($product_id, 'outofstock', 'product_visibility', true);
                update_post_meta($product_id, 'moca-product-availability', 'Εξαντλήθηκε');
            } elseif (($old_stock == 0 || $old_stock === null) && $stock > 0) {
                // 0 to positive: Set in stock
                $product->set_stock_status('instock');
                update_post_meta($product_id, '_stock_status', 'instock');
                wp_set_post_terms($product_id, 'instock', 'product_visibility');
                update_post_meta($product_id, 'moca-product-availability', 'Άμεσα διαθέσιμο');
            }

            $product->save();
            $products_to_clear_cache[] = $product_id;

            $results[] = [
                'sku' => $sku,
                'status' => 'success',
                'message' => 'Stock updated',
                'old_stock' => $old_stock,
                'new_stock' => $stock,
                'title' => $product->get_name(),
                'price' => $product->get_price(),
                'thumbnail' => get_the_post_thumbnail_url($product_id, 'thumbnail'),
            ];
        }
    }

    // 3. Clear cache for all updated products at once (bulk operation)
    foreach ($products_to_clear_cache as $product_id) {
        wc_delete_product_transients($product_id);
    }
    // Email summary using wp_mail
    $send_email = false;
    foreach ($results as $r) {
        if (isset($r['status']) && $r['status'] === 'success') {
            $send_email = true;
            break;
        }
    }
    if ($send_email) {
        $to = get_option('admin_email');
        $subject = 'Stock Update Results';
        $headers = ['Content-Type: text/html; charset=UTF-8'];
        $body = '<h2>Stock Update Results</h2><table border="1" cellpadding="6" cellspacing="0" style="border-collapse:collapse;"><tr><th>SKU</th><th>Title</th><th>Status</th><th>Message</th><th>Old Stock</th><th>New Stock</th><th>Price</th><th>Thumbnail</th></tr>';
        foreach ($results as $r) {
            $body .= '<tr>'
                . '<td>' . esc_html($r['sku'] ?? '') . '</td>'
                . '<td>' . esc_html($r['title'] ?? '') . '</td>'
                . '<td>' . esc_html($r['status'] ?? '') . '</td>'
                . '<td>' . esc_html($r['message'] ?? '') . '</td>'
                . '<td>' . esc_html($r['old_stock'] ?? '') . '</td>'
                . '<td>' . esc_html($r['new_stock'] ?? '') . '</td>'
                . '<td>' . esc_html($r['price'] ?? '') . '</td>'
                . '<td>' . ($r['thumbnail'] ? '<img src="' . esc_url($r['thumbnail']) . '" style="max-width:60px;max-height:60px;">' : '') . '</td>'
                . '</tr>';
        }
        $body .= '</table>';
        wp_mail($to, $subject, $body, $headers);
    }
    return $results;
}

// Update Price endpoint
function gk_update_price_callback($request) {
    $params = $request->get_json_params();
    $skus = isset($params['sku']) ? $params['sku'] : [];
    $regular_prices = isset($params['regular_price']) ? $params['regular_price'] : [];
    $sale_prices = isset($params['sale_price']) ? $params['sale_price'] : [];
    
    if (!is_array($skus)) $skus = [$skus];
    if (!is_array($regular_prices)) $regular_prices = [$regular_prices];
    if (!is_array($sale_prices)) $sale_prices = [$sale_prices];

    $results = [];
    $products_to_clear_cache = []; // Collect products for bulk cache clearing

    foreach ($skus as $i => $sku) {
        $regular_price = isset($regular_prices[$i]) ? $regular_prices[$i] : null;
        $sale_price = isset($sale_prices[$i]) ? $sale_prices[$i] : null;

        // a. Exact SKU match only
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU not found: ' . esc_html($sku)];
            continue;
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Product not found for SKU: ' . esc_html($sku)];
            continue;
        }

        // Verify exact SKU match (not partial)
        if ($product->get_sku() !== $sku) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU mismatch - found: ' . esc_html($product->get_sku())];
            continue;
        }

        // Handle both simple and variable products
        if ($product->is_type('variable')) {
            // For variable products, find the exact variation by SKU
            $variations = $product->get_children();
            $variation_updated = false;

            foreach ($variations as $variation_id) {
                $variation = wc_get_product($variation_id);
                if ($variation && $variation->get_sku() === $sku) {
                    $old_regular_price = $variation->get_regular_price();
                    $old_sale_price = $variation->get_sale_price();

                    // Handle regular price
                    if ($regular_price !== null && $regular_price !== '') {
                        if ($regular_price === '---') {
                            // c. Remove value if "---"
                            $variation->set_regular_price('');
                        } else {
                            // d. Sanitize price to prevent database issues
                            $sanitized_regular = floatval($regular_price);
                            if ($sanitized_regular >= 0) {
                                $variation->set_regular_price($sanitized_regular);
                            }
                        }
                    }
                    // b. If empty, keep current value (do nothing)

                    // Handle sale price
                    if ($sale_price !== null && $sale_price !== '') {
                        if ($sale_price === '---') {
                            // c. Remove value if "---"
                            $variation->set_sale_price('');
                        } else {
                            // d. Sanitize price to prevent database issues
                            $sanitized_sale = floatval($sale_price);
                            if ($sanitized_sale >= 0) {
                                $variation->set_sale_price($sanitized_sale);
                            }
                        }
                    }
                    // b. If empty, keep current value (do nothing)

                    $variation->save();
                    $products_to_clear_cache[] = $variation_id;
                    $variation_updated = true;

                    $results[] = [
                        'sku' => $sku,
                        'status' => 'success',
                        'message' => 'Variation price updated',
                        'old_regular_price' => $old_regular_price,
                        'new_regular_price' => $variation->get_regular_price(),
                        'old_sale_price' => $old_sale_price,
                        'new_sale_price' => $variation->get_sale_price(),
                        'title' => $variation->get_name(),
                        'thumbnail' => get_the_post_thumbnail_url($variation_id, 'thumbnail'),
                    ];
                    break;
                }
            }

            if (!$variation_updated) {
                $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Variation with exact SKU not found'];
            }
        } else {
            // Simple product
            $old_regular_price = $product->get_regular_price();
            $old_sale_price = $product->get_sale_price();

            // Handle regular price
            if ($regular_price !== null && $regular_price !== '') {
                if ($regular_price === '---') {
                    // c. Remove value if "---"
                    $product->set_regular_price('');
                } else {
                    // d. Sanitize price to prevent database issues
                    $sanitized_regular = floatval($regular_price);
                    if ($sanitized_regular >= 0) {
                        $product->set_regular_price($sanitized_regular);
                    }
                }
            }
            // b. If empty, keep current value (do nothing)

            // Handle sale price
            if ($sale_price !== null && $sale_price !== '') {
                if ($sale_price === '---') {
                    // c. Remove value if "---"
                    $product->set_sale_price('');
                } else {
                    // d. Sanitize price to prevent database issues
                    $sanitized_sale = floatval($sale_price);
                    if ($sanitized_sale >= 0) {
                        $product->set_sale_price($sanitized_sale);
                    }
                }
            }
            // b. If empty, keep current value (do nothing)

            $product->save();
            $products_to_clear_cache[] = $product_id;

            $results[] = [
                'sku' => $sku,
                'status' => 'success',
                'message' => 'Price updated',
                'old_regular_price' => $old_regular_price,
                'new_regular_price' => $product->get_regular_price(),
                'old_sale_price' => $old_sale_price,
                'new_sale_price' => $product->get_sale_price(),
                'title' => $product->get_name(),
                'thumbnail' => get_the_post_thumbnail_url($product_id, 'thumbnail'),
            ];
        }
    }

    // Clear cache for all updated products at once (bulk operation)
    foreach ($products_to_clear_cache as $product_id) {
        wc_delete_product_transients($product_id);
    }
    
    // Email notification logic similar to stock updates
    // ...

    return $results;
}
