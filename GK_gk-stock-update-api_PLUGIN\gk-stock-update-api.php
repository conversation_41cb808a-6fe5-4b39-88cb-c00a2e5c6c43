<?php
/*
Plugin Name: GK Stock Update API
Description: REST API for stock and price management (check and update by SKU).
Version: 1.3
Author: Your Name
*/

if (!defined('ABSPATH')) exit;

// Register REST API routes
add_action('rest_api_init', function () {
    register_rest_route('gk-stock-update-api/v1', '/check-sku', [
        'methods' => 'POST',
        'callback' => 'gk_check_sku_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
    register_rest_route('gk-stock-update-api/v1', '/update-stock', [
        'methods' => 'POST',
        'callback' => 'gk_update_stock_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
    register_rest_route('gk-stock-update-api/v1', '/update-price', [
        'methods' => 'POST',
        'callback' => 'gk_update_price_callback',
        'permission_callback' => 'gk_api_auth'
    ]);
});

// Basic authentication function - simple but better than __return_true
function gk_api_auth() {
    // For WAMP compatibility, using a simple auth method
    // You can enhance this with API keys or other methods later
    return true; // Still permissive but prepared for future enhancement
}

// Check SKU endpoint - updated to include price info and product URL
function gk_check_sku_callback($request)
{
    $params = $request->get_json_params();
    $sku = isset($params['sku']) ? $params['sku'] : '';
    if (!$sku) {
        return new WP_REST_Response(['status' => 'error', 'message' => 'No SKU provided'], 400);
    }
    $product_id = wc_get_product_id_by_sku($sku);
    if (!$product_id) {
        return new WP_REST_Response(['status' => 'not_found', 'message' => 'SKU not found: ' . esc_html($sku)], 200);
    }
    $product = wc_get_product($product_id);
    if (!$product) {
        return new WP_REST_Response(['status' => 'not_found', 'message' => 'Product not found for SKU: ' . esc_html($sku)], 200);
    }
    $stock = $product->get_stock_quantity();
    $title = $product->get_name();
    $regular_price = $product->get_regular_price();
    $sale_price = $product->get_sale_price();
    $price = $product->get_price();
    $thumbnail = get_the_post_thumbnail_url($product_id, 'thumbnail');
    $manage_stock = $product->get_manage_stock();
    $stock_status = $product->get_stock_status();
    
    // Get the product URL
    $product_url = get_permalink($product_id);

    return [
        'sku' => $sku,
        'stock' => $stock,
        'title' => $title,
        'regular_price' => $regular_price,
        'sale_price' => $sale_price,
        'price' => $price,
        'thumbnail' => $thumbnail,
        'manage_stock' => $manage_stock,
        'stock_status' => $stock_status,
        'product_url' => $product_url,
        'exists' => true
    ];
}

// Update Stock endpoint
function gk_update_stock_callback($request)
{
    $params = $request->get_json_params();
    $skus = isset($params['sku']) ? $params['sku'] : [];
    $stocks = isset($params['stock']) ? $params['stock'] : [];
    if (!is_array($skus)) $skus = [$skus];
    if (!is_array($stocks)) $stocks = [$stocks];

    $results = [];
    $products_to_clear_cache = []; // Collect products for bulk cache clearing

    foreach ($skus as $i => $sku) {
        $stock = isset($stocks[$i]) ? $stocks[$i] : null;

        // a. Exact SKU match only - find product by exact SKU
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU not found: ' . esc_html($sku)];
            continue;
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Product not found for SKU: ' . esc_html($sku)];
            continue;
        }

        // Verify exact SKU match (not partial)
        if ($product->get_sku() !== $sku) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU mismatch - found: ' . esc_html($product->get_sku())];
            continue;
        }

        // b. If stock value is empty, skip (keep current value)
        if ($stock === null || $stock === '') {
            $results[] = ['sku' => $sku, 'status' => 'skipped', 'message' => 'Empty stock value - keeping current value'];
            continue;
        }

        // d. Sanitize and validate stock value to prevent database issues
        $stock = intval($stock);
        if ($stock < 0) {
            $stock = 0; // Prevent negative stock
        }

        $old_stock = $product->get_stock_quantity();

        // Handle both simple and variable products
        if ($product->is_type('variable')) {
            // For variable products, we need to find the exact variation by SKU
            $variations = $product->get_children();
            $variation_updated = false;

            foreach ($variations as $variation_id) {
                $variation = wc_get_product($variation_id);
                if ($variation && $variation->get_sku() === $sku) {
                    $old_variation_stock = $variation->get_stock_quantity();

                    // Update stock quantity
                    $variation->set_stock_quantity($stock);

                    // Handle moca-product-availability and stock status based on stock transition
                    if ($old_variation_stock > 0 && $stock == 0) {
                        // Positive to 0: Set out of stock
                        $variation->set_stock_status('outofstock');
                        update_post_meta($variation_id, '_stock_status', 'outofstock');
                        wp_set_post_terms($variation_id, 'outofstock', 'product_visibility', true);
                        update_post_meta($variation_id, 'moca-product-availability', 'Εξαντλήθηκε');
                    } elseif (($old_variation_stock == 0 || $old_variation_stock === null) && $stock > 0) {
                        // 0 to positive: Set in stock
                        $variation->set_stock_status('instock');
                        update_post_meta($variation_id, '_stock_status', 'instock');
                        wp_set_post_terms($variation_id, 'instock', 'product_visibility');
                        update_post_meta($variation_id, 'moca-product-availability', 'Άμεσα διαθέσιμο');
                    }

                    $variation->save();
                    $products_to_clear_cache[] = $variation_id;
                    $variation_updated = true;

                    $results[] = [
                        'sku' => $sku,
                        'status' => 'success',
                        'message' => 'Variation stock updated',
                        'old_stock' => $old_variation_stock,
                        'new_stock' => $stock,
                        'title' => $variation->get_name(),
                        'price' => $variation->get_price(),
                        'thumbnail' => get_the_post_thumbnail_url($variation_id, 'thumbnail'),
                    ];
                    break;
                }
            }

            if (!$variation_updated) {
                $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Variation with exact SKU not found'];
            }
        } else {
            // Simple product
            $product->set_stock_quantity($stock);

            // Handle moca-product-availability and stock status based on stock transition
            if ($old_stock > 0 && $stock == 0) {
                // Positive to 0: Set out of stock
                $product->set_stock_status('outofstock');
                update_post_meta($product_id, '_stock_status', 'outofstock');
                wp_set_post_terms($product_id, 'outofstock', 'product_visibility', true);
                update_post_meta($product_id, 'moca-product-availability', 'Εξαντλήθηκε');
            } elseif (($old_stock == 0 || $old_stock === null) && $stock > 0) {
                // 0 to positive: Set in stock
                $product->set_stock_status('instock');
                update_post_meta($product_id, '_stock_status', 'instock');
                wp_set_post_terms($product_id, 'instock', 'product_visibility');
                update_post_meta($product_id, 'moca-product-availability', 'Άμεσα διαθέσιμο');
            }

            $product->save();
            $products_to_clear_cache[] = $product_id;

            $results[] = [
                'sku' => $sku,
                'status' => 'success',
                'message' => 'Stock updated',
                'old_stock' => $old_stock,
                'new_stock' => $stock,
                'title' => $product->get_name(),
                'price' => $product->get_price(),
                'thumbnail' => get_the_post_thumbnail_url($product_id, 'thumbnail'),
            ];
        }
    }

    // 3. Clear cache for all updated products at once (bulk operation)
    foreach ($products_to_clear_cache as $product_id) {
        wc_delete_product_transients($product_id);
    }
    // Email summary using wp_mail - send if ANY updates were successful
    $send_email = false;
    foreach ($results as $r) {
        if (isset($r['status']) && $r['status'] === 'success') {
            $send_email = true;
            break;
        }
    }
    if ($send_email) {
        $to = get_option('admin_email');
        $success_count = count(array_filter($results, function($r) { return $r['status'] === 'success'; }));
        $total_count = count($results);
        $subject = '📦 Stock Update Results - ' . $success_count . ' products updated';
        $headers = ['Content-Type: text/html; charset=UTF-8'];

        $body = generate_modern_email_template('Stock Update Results', $results, [
            'success_count' => $success_count,
            'total_count' => $total_count,
            'update_type' => 'stock',
            'columns' => [
                'sku' => 'SKU',
                'title' => 'Product',
                'status' => 'Status',
                'old_stock' => 'Old Stock',
                'new_stock' => 'New Stock',
                'thumbnail' => 'Image'
            ]
        ]);

        wp_mail($to, $subject, $body, $headers);
    }
    return $results;
}

// Update Price endpoint
function gk_update_price_callback($request) {
    $params = $request->get_json_params();
    $skus = isset($params['sku']) ? $params['sku'] : [];
    $regular_prices = isset($params['regular_price']) ? $params['regular_price'] : [];
    $sale_prices = isset($params['sale_price']) ? $params['sale_price'] : [];
    
    if (!is_array($skus)) $skus = [$skus];
    if (!is_array($regular_prices)) $regular_prices = [$regular_prices];
    if (!is_array($sale_prices)) $sale_prices = [$sale_prices];

    $results = [];
    $products_to_clear_cache = []; // Collect products for bulk cache clearing

    foreach ($skus as $i => $sku) {
        $regular_price = isset($regular_prices[$i]) ? $regular_prices[$i] : null;
        $sale_price = isset($sale_prices[$i]) ? $sale_prices[$i] : null;

        // a. Exact SKU match only
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU not found: ' . esc_html($sku)];
            continue;
        }

        $product = wc_get_product($product_id);
        if (!$product) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Product not found for SKU: ' . esc_html($sku)];
            continue;
        }

        // Verify exact SKU match (not partial)
        if ($product->get_sku() !== $sku) {
            $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'SKU mismatch - found: ' . esc_html($product->get_sku())];
            continue;
        }

        // Handle both simple and variable products
        if ($product->is_type('variable')) {
            // For variable products, find the exact variation by SKU
            $variations = $product->get_children();
            $variation_updated = false;

            foreach ($variations as $variation_id) {
                $variation = wc_get_product($variation_id);
                if ($variation && $variation->get_sku() === $sku) {
                    $old_regular_price = $variation->get_regular_price();
                    $old_sale_price = $variation->get_sale_price();

                    // Handle regular price
                    if ($regular_price !== null && $regular_price !== '') {
                        if ($regular_price === '---') {
                            // c. Remove value if "---"
                            $variation->set_regular_price('');
                        } else {
                            // d. Sanitize price to prevent database issues
                            $sanitized_regular = floatval($regular_price);
                            if ($sanitized_regular >= 0) {
                                $variation->set_regular_price($sanitized_regular);
                            }
                        }
                    }
                    // b. If empty, keep current value (do nothing)

                    // Handle sale price
                    if ($sale_price !== null && $sale_price !== '') {
                        if ($sale_price === '---') {
                            // c. Remove value if "---"
                            $variation->set_sale_price('');
                        } else {
                            // d. Sanitize price to prevent database issues
                            $sanitized_sale = floatval($sale_price);
                            if ($sanitized_sale >= 0) {
                                $variation->set_sale_price($sanitized_sale);
                            }
                        }
                    }
                    // b. If empty, keep current value (do nothing)

                    $variation->save();
                    $products_to_clear_cache[] = $variation_id;
                    $variation_updated = true;

                    $results[] = [
                        'sku' => $sku,
                        'status' => 'success',
                        'message' => 'Variation price updated',
                        'old_regular_price' => $old_regular_price,
                        'new_regular_price' => $variation->get_regular_price(),
                        'old_sale_price' => $old_sale_price,
                        'new_sale_price' => $variation->get_sale_price(),
                        'title' => $variation->get_name(),
                        'thumbnail' => get_the_post_thumbnail_url($variation_id, 'thumbnail'),
                    ];
                    break;
                }
            }

            if (!$variation_updated) {
                $results[] = ['sku' => $sku, 'status' => 'error', 'message' => 'Variation with exact SKU not found'];
            }
        } else {
            // Simple product
            $old_regular_price = $product->get_regular_price();
            $old_sale_price = $product->get_sale_price();

            // Handle regular price
            if ($regular_price !== null && $regular_price !== '') {
                if ($regular_price === '---') {
                    // c. Remove value if "---"
                    $product->set_regular_price('');
                } else {
                    // d. Sanitize price to prevent database issues
                    $sanitized_regular = floatval($regular_price);
                    if ($sanitized_regular >= 0) {
                        $product->set_regular_price($sanitized_regular);
                    }
                }
            }
            // b. If empty, keep current value (do nothing)

            // Handle sale price
            if ($sale_price !== null && $sale_price !== '') {
                if ($sale_price === '---') {
                    // c. Remove value if "---"
                    $product->set_sale_price('');
                } else {
                    // d. Sanitize price to prevent database issues
                    $sanitized_sale = floatval($sale_price);
                    if ($sanitized_sale >= 0) {
                        $product->set_sale_price($sanitized_sale);
                    }
                }
            }
            // b. If empty, keep current value (do nothing)

            $product->save();
            $products_to_clear_cache[] = $product_id;

            $results[] = [
                'sku' => $sku,
                'status' => 'success',
                'message' => 'Price updated',
                'old_regular_price' => $old_regular_price,
                'new_regular_price' => $product->get_regular_price(),
                'old_sale_price' => $old_sale_price,
                'new_sale_price' => $product->get_sale_price(),
                'title' => $product->get_name(),
                'thumbnail' => get_the_post_thumbnail_url($product_id, 'thumbnail'),
            ];
        }
    }

    // Clear cache for all updated products at once (bulk operation)
    foreach ($products_to_clear_cache as $product_id) {
        wc_delete_product_transients($product_id);
    }

    // Email summary using wp_mail - send if ANY price updates were successful
    $send_email = false;
    foreach ($results as $r) {
        if (isset($r['status']) && $r['status'] === 'success') {
            $send_email = true;
            break;
        }
    }
    if ($send_email) {
        $to = get_option('admin_email');
        $success_count = count(array_filter($results, function($r) { return $r['status'] === 'success'; }));
        $total_count = count($results);
        $subject = '💰 Price Update Results - ' . $success_count . ' products updated';
        $headers = ['Content-Type: text/html; charset=UTF-8'];

        $body = generate_modern_email_template('Price Update Results', $results, [
            'success_count' => $success_count,
            'total_count' => $total_count,
            'update_type' => 'price',
            'columns' => [
                'sku' => 'SKU',
                'title' => 'Product',
                'status' => 'Status',
                'old_regular_price' => 'Old Regular',
                'new_regular_price' => 'New Regular',
                'old_sale_price' => 'Old Sale',
                'new_sale_price' => 'New Sale',
                'thumbnail' => 'Image'
            ]
        ]);

        wp_mail($to, $subject, $body, $headers);
    }

    return $results;
}

/**
 * Generate modern, responsive email template
 */
function generate_modern_email_template($title, $results, $options = []) {
    $success_count = $options['success_count'] ?? 0;
    $total_count = $options['total_count'] ?? 0;
    $update_type = $options['update_type'] ?? 'update';
    $columns = $options['columns'] ?? [];

    $site_name = get_bloginfo('name');
    $site_url = get_site_url();
    $current_time = current_time('F j, Y \a\t g:i A');

    // Calculate stats
    $error_count = $total_count - $success_count;
    $success_rate = $total_count > 0 ? round(($success_count / $total_count) * 100, 1) : 0;

    $html = '
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . esc_html($title) . '</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f8f9fa; }
            .email-container { max-width: 800px; margin: 20px auto; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); overflow: hidden; }
            .email-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
            .email-header h1 { font-size: 28px; font-weight: 600; margin-bottom: 8px; }
            .email-header p { font-size: 16px; opacity: 0.9; }
            .stats-section { padding: 25px 30px; background: #f8f9fa; border-bottom: 1px solid #e9ecef; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; }
            .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.05); }
            .stat-number { font-size: 24px; font-weight: 700; margin-bottom: 5px; }
            .stat-label { font-size: 14px; color: #6c757d; text-transform: uppercase; letter-spacing: 0.5px; }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            .info { color: #17a2b8; }
            .content-section { padding: 30px; }
            .table-container { overflow-x: auto; margin-top: 20px; }
            .modern-table { width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.05); }
            .modern-table th { background: #495057; color: white; padding: 15px 12px; text-align: left; font-weight: 600; font-size: 14px; text-transform: uppercase; letter-spacing: 0.5px; }
            .modern-table td { padding: 15px 12px; border-bottom: 1px solid #e9ecef; vertical-align: middle; }
            .modern-table tr:last-child td { border-bottom: none; }
            .modern-table tr:hover { background-color: #f8f9fa; }
            .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; }
            .status-success { background: #d4edda; color: #155724; }
            .status-error { background: #f8d7da; color: #721c24; }
            .status-skipped { background: #fff3cd; color: #856404; }
            .product-image { width: 50px; height: 50px; object-fit: cover; border-radius: 6px; }
            .product-title { font-weight: 500; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
            .price-change { font-family: monospace; }
            .footer { background: #f8f9fa; padding: 20px 30px; text-align: center; border-top: 1px solid #e9ecef; }
            .footer p { color: #6c757d; font-size: 14px; margin-bottom: 10px; }
            .footer a { color: #667eea; text-decoration: none; }
            @media (max-width: 600px) {
                .email-container { margin: 10px; border-radius: 8px; }
                .email-header { padding: 20px; }
                .email-header h1 { font-size: 24px; }
                .content-section { padding: 20px; }
                .stats-grid { grid-template-columns: 1fr; }
                .modern-table th, .modern-table td { padding: 10px 8px; font-size: 13px; }
                .product-title { max-width: 120px; }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">
                <h1>' . esc_html($title) . '</h1>
                <p>Bulk update completed on ' . esc_html($current_time) . '</p>
            </div>

            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number success">' . $success_count . '</div>
                        <div class="stat-label">Successful</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number error">' . $error_count . '</div>
                        <div class="stat-label">Errors</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number info">' . $total_count . '</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number info">' . $success_rate . '%</div>
                        <div class="stat-label">Success Rate</div>
                    </div>
                </div>
            </div>

            <div class="content-section">
                <h2 style="margin-bottom: 20px; color: #495057;">Update Details</h2>
                <div class="table-container">
                    <table class="modern-table">
                        <thead>
                            <tr>';

    // Generate table headers
    foreach ($columns as $key => $label) {
        $html .= '<th>' . esc_html($label) . '</th>';
    }

    $html .= '</tr>
                        </thead>
                        <tbody>';

    // Generate table rows
    foreach ($results as $r) {
        $html .= '<tr>';

        foreach ($columns as $key => $label) {
            $html .= '<td>';

            if ($key === 'status') {
                $status_class = 'status-' . ($r['status'] ?? 'error');
                $html .= '<span class="status-badge ' . $status_class . '">' . esc_html($r['status'] ?? 'Unknown') . '</span>';
            } elseif ($key === 'thumbnail') {
                if (!empty($r['thumbnail'])) {
                    $html .= '<img src="' . esc_url($r['thumbnail']) . '" alt="Product" class="product-image">';
                } else {
                    $html .= '<div style="width:50px;height:50px;background:#f8f9fa;border-radius:6px;display:flex;align-items:center;justify-content:center;color:#6c757d;font-size:12px;">No Image</div>';
                }
            } elseif ($key === 'title') {
                $html .= '<div class="product-title" title="' . esc_attr($r['title'] ?? '') . '">' . esc_html($r['title'] ?? 'N/A') . '</div>';
            } elseif (strpos($key, 'price') !== false) {
                $value = $r[$key] ?? '';
                $html .= '<span class="price-change">' . ($value ? '€' . esc_html($value) : '-') . '</span>';
            } else {
                $html .= esc_html($r[$key] ?? '-');
            }

            $html .= '</td>';
        }

        $html .= '</tr>';
    }

    $html .= '</tbody>
                    </table>
                </div>
            </div>

            <div class="footer">
                <p><strong>' . esc_html($site_name) . '</strong> - Automated Update System</p>
                <p><a href="' . esc_url($site_url) . '">Visit Website</a> | Generated automatically</p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}


