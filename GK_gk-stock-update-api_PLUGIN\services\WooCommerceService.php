<?php
class WooCommerceService
{
    private $product_cache = [];
    
    /**
     * Get a product by SKU with caching
     */
    public function get_product_by_sku($sku)
    {
        if (isset($this->product_cache[$sku])) {
            return $this->product_cache[$sku];
        }
        
        $product_id = wc_get_product_id_by_sku($sku);
        if (!$product_id) {
            $this->product_cache[$sku] = null;
            return null;
        }
        
        $product = wc_get_product($product_id);
        $this->product_cache[$sku] = $product;
        return $product;
    }
    
    /**
     * Update product stock
     * 
     * @param string $sku The product SKU
     * @param int $stock The new stock quantity
     * @return array Result of the operation
     */
    public function update_product_stock($sku, $stock)
    {
        $product = $this->get_product_by_sku($sku);
        
        if (!$product) {
            return [
                'sku' => $sku,
                'status' => 'error',
                'message' => 'Product not found'
            ];
        }
        
        $old_stock = $product->get_stock_quantity();
        $product->set_stock_quantity($stock);
        $product->save();
        
        return [
            'sku' => $sku,
            'status' => 'success',
            'message' => 'Stock updated',
            'old_stock' => $old_stock,
            'new_stock' => $stock,
            'title' => $product->get_name(),
            'price' => $product->get_price(),
            'thumbnail' => get_the_post_thumbnail_url($product->get_id(), 'thumbnail'),
            'product_url' => get_permalink($product->get_id()),
        ];
    }
    
    /**
     * Update product prices
     * 
     * @param string $sku The product SKU
     * @param float|null $regular_price The new regular price (null to keep current)
     * @param float|null $sale_price The new sale price (null to keep current)
     * @return array Result of the operation
     */
    public function update_product_prices($sku, $regular_price = null, $sale_price = null)
    {
        $product = $this->get_product_by_sku($sku);
        
        if (!$product) {
            return [
                'sku' => $sku,
                'status' => 'error',
                'message' => 'Product not found'
            ];
        }
        
        $old_regular_price = $product->get_regular_price();
        $old_sale_price = $product->get_sale_price();
        
        if ($regular_price !== null && $regular_price !== '') {
            $product->set_regular_price($regular_price);
        }
        
        if ($sale_price !== null && $sale_price !== '') {
            $product->set_sale_price($sale_price);
        }
        
        $product->save();
        
        return [
            'sku' => $sku,
            'status' => 'success',
            'message' => 'Price updated',
            'old_regular_price' => $old_regular_price,
            'new_regular_price' => $regular_price !== null ? $regular_price : $old_regular_price,
            'old_sale_price' => $old_sale_price,
            'new_sale_price' => $sale_price !== null ? $sale_price : $old_sale_price,
            'title' => $product->get_name(),
            'thumbnail' => get_the_post_thumbnail_url($product->get_id(), 'thumbnail'),
            'product_url' => get_permalink($product->get_id()),
        ];
    }
}
